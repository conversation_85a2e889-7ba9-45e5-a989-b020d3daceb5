<?php if(get_payment_setting('status', TABBY_PAYMENT_METHOD_NAME) == 1): ?>
    <?php
        $paymentService = new Botble\Tabby\Services\Gateways\TabbyPaymentService();
    ?>

    <?php if (isset($component)) { $__componentOriginal69057bacd9705b1c669802ff37556f6e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69057bacd9705b1c669802ff37556f6e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'fa081de1c3ee47622336b4eeafa01705::payment-method','data' => ['name' => TABBY_PAYMENT_METHOD_NAME,'paymentName' => 'Tabby Pay-in-4','supportedCurrencies' => $paymentService->supportedCurrencyCodes()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('plugins-payment::payment-method'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(TABBY_PAYMENT_METHOD_NAME),'paymentName' => 'Tabby Pay-in-4','supportedCurrencies' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($paymentService->supportedCurrencyCodes())]); ?>
         <?php $__env->slot('currencyNotSupportedMessage', null, []); ?> 
            <p class="mt-1 mb-0">
                <?php echo e(__('Learn more')); ?>:
                <?php echo e(Html::link('https://tabby.ai/en/supported-countries/', attributes: ['target' => '_blank', 'rel' => 'nofollow'])); ?>.
            </p>
         <?php $__env->endSlot(); ?>

        <?php if($errorMessage): ?>
            <div class="text-danger my-2">
                <?php echo BaseHelper::clean($errorMessage); ?>

            </div>
        <?php endif; ?>

        <?php if(!empty($rejectionReason) && !$isEligible): ?>
            <div class="alert alert-warning my-2">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo e($rejectionMessage); ?>

            </div>
        <?php elseif($isEligible): ?>
            <div class="tabby-payment-info my-2">
                <div class="d-flex align-items-center mb-2">
                    <img src="<?php echo e(url('vendor/core/plugins/tabby/images/tabby.svg')); ?>" alt="Tabby" style="height: 24px; margin-right: 8px;">
                    <span class="text-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo e(__('Pay in 4 installments with Tabby')); ?>

                    </span>
                </div>
                <div class="text-muted small">
                    <?php echo e(__('Split your purchase into 4 interest-free payments. No fees when you pay on time.')); ?>

                </div>
                <div class="tabby-card" id="tabbyCard"></div>
            </div>
        <?php endif; ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69057bacd9705b1c669802ff37556f6e)): ?>
<?php $attributes = $__attributesOriginal69057bacd9705b1c669802ff37556f6e; ?>
<?php unset($__attributesOriginal69057bacd9705b1c669802ff37556f6e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69057bacd9705b1c669802ff37556f6e)): ?>
<?php $component = $__componentOriginal69057bacd9705b1c669802ff37556f6e; ?>
<?php unset($__componentOriginal69057bacd9705b1c669802ff37556f6e); ?>
<?php endif; ?>

    <?php if($isEligible): ?>
        
            <script src="https://checkout.tabby.ai/tabby-card.js"></script>
            <script src="<?php echo e(asset('vendor/core/plugins/tabby/js/tabby-card.js')); ?>"></script>

            
            <script>
                // Make checkout data available to the Tabby card manager
                window.TabbyCheckoutData = {
                    amount: '<?php echo e(isset($amount) ? preg_replace("/[^0-9.]/", "", $amount) : "0"); ?>',
                    currency: '<?php echo e(strtoupper(get_application_currency()->title)); ?>',
                    locale: '<?php echo e(app()->getLocale() === "ar" ? "ar" : "en"); ?>',
                    paymentMethodName: '<?php echo e(TABBY_PAYMENT_METHOD_NAME); ?>'
                };
            </script>
        
    <?php endif; ?>
<?php endif; ?>

<?php /**PATH D:\laragon\www\martfury\platform/plugins/tabby/resources/views/methods.blade.php ENDPATH**/ ?>