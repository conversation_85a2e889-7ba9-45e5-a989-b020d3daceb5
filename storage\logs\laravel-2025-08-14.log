[2025-08-14 23:17:28] local.INFO: HyperPay makePayment called with data: {"amount":353.1,"currency":"SAR","order_id":[46],"payment_type":"visa","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"<PERSON><PERSON>","email":"<EMAIL>","phone":"+18598017656","country":null,"state":null,"city":null,"address":null,"zip_code":null}} 
[2025-08-14 23:17:28] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Abstracts\\HyperPayPaymentAbstract.php:119)
[stacktrace]
#0 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Abstracts\\HyperPayPaymentAbstract.php(119): trim(Array)
#1 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Gateways\\HyperPayPaymentService.php(31): Botble\\HyperPay\\Services\\Abstracts\\HyperPayPaymentAbstract->createCheckout(Array)
#2 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Abstracts\\HyperPayPaymentAbstract.php(34): Botble\\HyperPay\\Services\\Gateways\\HyperPayPaymentService->makePayment(Array)
#3 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Providers\\HookServiceProvider.php(159): Botble\\HyperPay\\Services\\Abstracts\\HyperPayPaymentAbstract->execute(Array)
#4 [internal function]: Botble\\HyperPay\\Providers\\HookServiceProvider->checkoutWithHyperPay(Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#5 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#6 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('payment-after-p...', Array)
#7 D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#8 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php(789): apply_filters('payment-after-p...', Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#9 [internal function]: Botble\\Ecommerce\\Providers\\HookServiceProvider->Botble\\Ecommerce\\Providers\\{closure}(Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#10 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Object(Closure), Array)
#11 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('filter_ecommerc...', Array)
#12 D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#13 D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php(622): apply_filters('filter_ecommerc...', Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#14 D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php(235): Botble\\Marketplace\\Providers\\OrderSupportServiceProvider->processPaymentMethodPostCheckout(Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), 353.1)
#15 [internal function]: Botble\\Marketplace\\Providers\\OrderSupportServiceProvider->processPostCheckoutOrder(Object(Illuminate\\Database\\Eloquent\\Collection), Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), '50da09dec9a537b...', Array, Object(Botble\\Base\\Http\\Responses\\BaseHttpResponse))
#16 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#17 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('post_checkout_o...', Array)
#18 D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#19 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php(690): apply_filters('post_checkout_o...', Object(Illuminate\\Database\\Eloquent\\Collection), Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), '50da09dec9a537b...', Array, Object(Botble\\Base\\Http\\Responses\\BaseHttpResponse))
#20 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController->postCheckout('50da09dec9a537b...', Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), Object(Botble\\Ecommerce\\Services\\HandleShippingFeeService), Object(Botble\\Ecommerce\\Services\\HandleApplyCouponService), Object(Botble\\Ecommerce\\Services\\HandleRemoveCouponService), Object(Botble\\Ecommerce\\Services\\HandleApplyPromotionsService))
#21 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('postCheckout', Array)
#22 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController), 'postCheckout')
#23 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#24 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#25 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\TrackAbandonedCart.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\TrackAbandonedCart->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\martfury\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\martfury\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#61 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\martfury\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#97 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\martfury\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#99 {main}
"} 
[2025-08-14 23:20:57] local.INFO: HyperPay makePayment called with data: {"amount":353.1,"currency":"SAR","order_id":[46],"payment_type":"visa","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"Fae Koelpin","email":"<EMAIL>","phone":"+18598017656","country":null,"state":null,"city":null,"address":null,"zip_code":null}} 
