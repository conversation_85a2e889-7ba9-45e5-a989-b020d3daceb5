[2025-08-14 23:27:43] local.INFO: HyperPay makePayment called with data: {"amount":353.1,"currency":"SAR","order_id":[46],"payment_type":"visa","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"<PERSON><PERSON>","email":"<EMAIL>","phone":"+18598017656","country":null,"state":null,"city":null,"address":null,"zip_code":null}}
[2025-08-14 23:27:43] local.INFO: HyperPay Checkout Request {"url":"https://eu-test.oppwa.com/v1/checkouts","entity_id":"8ac7a4c79483092601948366b9d1011b","amount":"353.10","currency":"SAR","payment_type":"visa","data":{"entityId":"8ac7a4c79483092601948366b9d1011b","amount":"353.10","currency":"SAR","paymentType":"DB","merchantTransactionId":"46","shopperResultUrl":"https://martfury.gc/payment/hyperpay/callback","customer.email":"<EMAIL>","customer.givenName":"Fae","customer.surname":"Koelpin","billing.street1":"Fae Koelpin","billing.city":"Riyadh","billing.state":"Riyadh","billing.country":"SA","billing.postcode":"11111"}}
[2025-08-14 23:27:45] local.INFO: HyperPay Checkout Response {"status_code":200,"response":{"result":{"code":"000.200.100","description":"successfully created checkout"},"buildNumber":"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000","timestamp":"2025-08-14 23:27:45+0000","ndc":"85659335676241087CEA05738D063202.uat01-vm-tx01","id":"85659335676241087CEA05738D063202.uat01-vm-tx01"}}
[2025-08-14 23:29:27] local.INFO: Testing HyperPay - Current time: 2025-08-14 23:29:27
[2025-08-14 23:30:35] local.INFO: HyperPay makePayment called with data: {"amount":353.1,"currency":"SAR","order_id":[46],"payment_type":"visa","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"Fae Koelpin","email":"<EMAIL>","phone":"+18598017656","country":null,"state":null,"city":null,"address":null,"zip_code":null}}
[2025-08-14 23:30:35] local.INFO: HyperPay Checkout Request {"url":"https://eu-test.oppwa.com/v1/checkouts","entity_id":"8ac7a4c79483092601948366b9d1011b","amount":"353.10","currency":"SAR","payment_type":"visa","data":{"entityId":"8ac7a4c79483092601948366b9d1011b","amount":"353.10","currency":"SAR","paymentType":"DB","merchantTransactionId":"46","shopperResultUrl":"https://martfury.gc/payment/hyperpay/callback","customer.email":"<EMAIL>","customer.givenName":"Fae","customer.surname":"Koelpin","billing.street1":"Fae Koelpin","billing.city":"Riyadh","billing.state":"Riyadh","billing.country":"SA","billing.postcode":"11111"}}
[2025-08-14 23:30:36] local.INFO: HyperPay Checkout Response {"status_code":200,"response":{"result":{"code":"000.200.100","description":"successfully created checkout"},"buildNumber":"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000","timestamp":"2025-08-14 23:30:37+0000","ndc":"771B0DF3DEB51D9C0CFB11CE044492D7.uat01-vm-tx01","id":"771B0DF3DEB51D9C0CFB11CE044492D7.uat01-vm-tx01"}}
[2025-08-14 23:38:45] local.INFO: HyperPay makePayment called with data: {"amount":353.1,"currency":"SAR","order_id":[46],"payment_type":"visa","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"Fae Koelpin","email":"<EMAIL>","phone":"+18598017656","country":null,"state":null,"city":null,"address":null,"zip_code":null}} 
[2025-08-14 23:38:45] local.INFO: HyperPay Checkout Request {"url":"https://eu-test.oppwa.com/v1/checkouts","entity_id":"8ac7a4c79483092601948366b9d1011b","amount":"353.10","currency":"SAR","payment_type":"visa","data":{"entityId":"8ac7a4c79483092601948366b9d1011b","amount":"353.10","currency":"SAR","paymentType":"DB","merchantTransactionId":"46","shopperResultUrl":"https://martfury.gc/payment/hyperpay/callback","customer.email":"<EMAIL>","customer.givenName":"Fae","customer.surname":"Koelpin","billing.street1":"Fae Koelpin","billing.city":"Riyadh","billing.state":"Riyadh","billing.country":"SA","billing.postcode":"11111"}} 
[2025-08-14 23:38:46] local.INFO: HyperPay Checkout Response {"status_code":200,"response":{"result":{"code":"000.200.100","description":"successfully created checkout"},"buildNumber":"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000","timestamp":"2025-08-14 23:38:47+0000","ndc":"C8D86D6C07583F77C181AD7A04BA532F.uat01-vm-tx03","id":"C8D86D6C07583F77C181AD7A04BA532F.uat01-vm-tx03"}} 
[2025-08-14 23:39:19] local.INFO: HyperPay makePayment called with data: {"amount":353.1,"currency":"SAR","order_id":[46],"payment_type":"visa","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"Fae Koelpin","email":"<EMAIL>","phone":"+18598017656","country":null,"state":null,"city":null,"address":null,"zip_code":null}} 
[2025-08-14 23:39:19] local.INFO: HyperPay Checkout Request {"url":"https://eu-test.oppwa.com/v1/checkouts","entity_id":"8ac7a4c79483092601948366b9d1011b","amount":"353.10","currency":"SAR","payment_type":"visa","data":{"entityId":"8ac7a4c79483092601948366b9d1011b","amount":"353.10","currency":"SAR","paymentType":"DB","merchantTransactionId":"46","shopperResultUrl":"https://martfury.gc/payment/hyperpay/callback","customer.email":"<EMAIL>","customer.givenName":"Fae","customer.surname":"Koelpin","billing.street1":"Fae Koelpin","billing.city":"Riyadh","billing.state":"Riyadh","billing.country":"SA","billing.postcode":"11111"}} 
[2025-08-14 23:39:19] local.INFO: HyperPay Checkout Response {"status_code":200,"response":{"result":{"code":"000.200.100","description":"successfully created checkout"},"buildNumber":"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000","timestamp":"2025-08-14 23:39:20+0000","ndc":"9B034E56604686EB9CE18B4A14F2641B.uat01-vm-tx02","id":"9B034E56604686EB9CE18B4A14F2641B.uat01-vm-tx02"}} 
