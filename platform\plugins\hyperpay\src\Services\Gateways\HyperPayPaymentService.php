<?php

namespace Bo<PERSON>ble\HyperPay\Services\Gateways;

use Bo<PERSON>ble\HyperPay\Services\Abstracts\HyperPayPaymentAbstract;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HyperPayPaymentService extends HyperPayPaymentAbstract
{
    public function makePayment(array $data): ?string
    {
        $this->amount = $data['amount'];
        $this->currency = strtoupper($data['currency']);

        try {
            $checkoutData = $this->createCheckout([
                'amount' => $this->amount,
                'currency' => $this->currency,
                'order_id' => $data['order_id'],
                'payment_type' => $data['payment_type'] ?? 'visa',
                'customer_email' => Arr::get($data, 'address.email'),
                'customer_name' => Arr::get($data, 'address.name'),
                'billing_address' => Arr::get($data, 'address.address'),
                'billing_city' => Arr::get($data, 'address.city'),
                'billing_country' => Arr::get($data, 'address.country', 'SA'),
                'billing_postcode' => Arr::get($data, 'address.zip', '11111'),
            ]);

            if (isset($checkoutData['id'])) {
                $this->checkoutId = $checkoutData['id'];

                // Store checkout data in session for later use
                session([
                    'hyperpay_checkout_id' => $this->checkoutId,
                    'hyperpay_order_id' => $data['order_id'],
                    'hyperpay_amount' => $this->amount,
                    'hyperpay_currency' => $this->currency,
                    'hyperpay_payment_type' => $data['payment_type'] ?? 'visa',
                ]);

                return $this->checkoutId;
            }

            throw new Exception('Failed to create HyperPay checkout');
        } catch (Exception $e) {
            Log::error('HyperPay Payment Error', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            $this->setErrorMessage($e->getMessage());
            return null;
        }
    }

    public function afterMakePayment(array $data): ?string
    {
        $request = request();
        $resourcePath = $request->get('resourcePath');
        $checkoutId = $request->get('id');

        if (!$resourcePath || !$checkoutId) {
            $this->setErrorMessage('Missing payment result parameters');
            return null;
        }

        try {
            $paymentResult = $this->getPaymentStatus($resourcePath, $checkoutId);

            if (!isset($paymentResult['result']['code'])) {
                throw new Exception('Invalid payment result response');
            }

            $resultCode = $paymentResult['result']['code'];
            $status = PaymentStatusEnum::FAILED;
            $chargeId = $paymentResult['id'] ?? $checkoutId;

            if ($this->isSuccessfulPayment($resultCode)) {
                $status = PaymentStatusEnum::COMPLETED;
            } elseif ($this->isPendingPayment($resultCode)) {
                $status = PaymentStatusEnum::PENDING;
            }

            // Get order information from session
            $orderIds = (array) session('hyperpay_order_id', []);
            $amount = session('hyperpay_amount', 0);
            $currency = session('hyperpay_currency', 'SAR');

            // Process the payment
            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
                'amount' => $amount,
                'currency' => $currency,
                'charge_id' => $chargeId,
                'order_id' => $orderIds,
                'customer_id' => Arr::get($data, 'customer_id'),
                'customer_type' => Arr::get($data, 'customer_type'),
                'payment_channel' => HYPERPAY_PAYMENT_METHOD_NAME,
                'status' => $status,
                'payment_response' => $paymentResult,
            ]);

            // Clear session data
            session()->forget([
                'hyperpay_checkout_id',
                'hyperpay_order_id',
                'hyperpay_amount',
                'hyperpay_currency',
                'hyperpay_payment_type'
            ]);

            return $chargeId;
        } catch (Exception $e) {
            Log::error('HyperPay After Payment Error', [
                'error' => $e->getMessage(),
                'checkout_id' => $checkoutId,
                'resource_path' => $resourcePath
            ]);
            $this->setErrorMessage($e->getMessage());
            return null;
        }
    }

    public function getPaymentDetails(string $chargeId): ?array
    {
        try {
            // For HyperPay, we would need to implement a payment details retrieval
            // This would typically involve calling their API with the transaction ID
            $url = $this->getApiUrl() . '/payments/' . $chargeId;
            $accessToken = $this->getAccessToken();

            if (!$accessToken) {
                return null;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ])->get($url);

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (Exception $e) {
            Log::error('HyperPay Get Payment Details Error', [
                'error' => $e->getMessage(),
                'charge_id' => $chargeId
            ]);
            return null;
        }
    }

    public function isValidToProcessCheckout(): bool
    {
        return apply_filters('hyperpay_is_valid_to_process_checkout', $this->isAvailable());
    }

    public function getOrderNotes(): array
    {
        return apply_filters('hyperpay_order_notes', []);
    }

    public function isAvailable(): bool
    {
        $accessToken = get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME);
        $visaEntityId = get_payment_setting('visa_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);

        return !empty($accessToken) && !empty($visaEntityId);
    }

    public function getCheckoutUrl(string $checkoutId): string
    {
        $isSandbox = get_payment_setting('sandbox_mode', HYPERPAY_PAYMENT_METHOD_NAME, false);

        $baseUrl = $isSandbox
            ? 'https://eu-test.oppwa.com/v1/paymentWidgets.js'
            : 'https://eu-prod.oppwa.com/v1/paymentWidgets.js';

        return $baseUrl . '?checkoutId=' . $checkoutId;
    }

    public function getShopperResultUrl(): string
    {
        return PaymentHelper::getRedirectURL();
    }

    public function getSupportedPaymentTypes(): array
    {
        return [
            'visa' => 'Visa/Mastercard',
            'mada' => 'Mada',
            'amex' => 'American Express',
            'applepay' => 'Apple Pay',
        ];
    }

    public function getAvailablePaymentTypes(): array
    {
        $availableTypes = [];
        $supportedTypes = $this->getSupportedPaymentTypes();

        foreach ($supportedTypes as $type => $label) {
            $entityId = $this->getEntityId($type);
            if (!empty($entityId)) {
                $availableTypes[$type] = $label;
            }
        }

        return $availableTypes;
    }
}
