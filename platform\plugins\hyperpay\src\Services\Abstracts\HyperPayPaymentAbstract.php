<?php

namespace Botble\HyperPay\Services\Abstracts;

use Botble\Payment\Services\Traits\PaymentErrorTrait;
use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

abstract class HyperPayPaymentAbstract
{
    use PaymentErrorTrait;

    protected float $amount;
    protected string $currency;
    protected string $checkoutId;
    protected bool $supportRefundOnline = false;
    protected array $supportedCurrencies = ['SAR', 'AED', 'USD', 'EUR'];

    public function getSupportRefundOnline(): bool
    {
        return $this->supportRefundOnline;
    }

    public function supportedCurrencyCodes(): array
    {
        return $this->supportedCurrencies;
    }

    public function execute(array $data): ?string
    {
        try {
            return $this->makePayment($data);
        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 1);
            return null;
        }
    }

    abstract public function makePayment(array $data): ?string;

    abstract public function afterMakePayment(array $data): ?string;

    public function getApiUrl(): string
    {
        $isSandbox = get_payment_setting('sandbox_mode', HYPERPAY_PAYMENT_METHOD_NAME, false);
        
        return $isSandbox 
            ? 'https://eu-test.oppwa.com/v1' 
            : 'https://eu-prod.oppwa.com/v1';
    }

    public function getAccessToken(): ?string
    {
        return get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME);
    }

    public function getEntityId(string $paymentType = 'visa'): ?string
    {
        $entityIdKey = match($paymentType) {
            'visa', 'master' => 'visa_entity_id',
            'mada' => 'mada_entity_id',
            'amex' => 'amex_entity_id',
            'applepay' => 'applepay_entity_id',
            default => 'visa_entity_id'
        };

        return get_payment_setting($entityIdKey, HYPERPAY_PAYMENT_METHOD_NAME);
    }

    public function createCheckout(array $data): array
    {
        $url = $this->getApiUrl() . '/checkouts';
        $accessToken = $this->getAccessToken();
        $entityId = $this->getEntityId($data['payment_type'] ?? 'visa');

        if (!$accessToken || !$entityId) {
            throw new Exception('HyperPay configuration is incomplete. Please check access token and entity IDs.');
        }

        $amount = number_format((float) $data['amount'], 2, '.', '');
        $currency = $data['currency'] ?? get_payment_setting('currency', HYPERPAY_PAYMENT_METHOD_NAME, 'SAR');
        $merchantTransactionId = $data['order_id'] ?? uniqid('order_');

        $postData = [
            'entityId' => $entityId,
            'amount' => $amount,
            'currency' => $currency,
            'paymentType' => 'DB',
            'merchantTransactionId' => $merchantTransactionId,
        ];

        // Add 3DS parameters if enabled
        if (get_payment_setting('3ds_enabled', HYPERPAY_PAYMENT_METHOD_NAME, true)) {
            $postData['customParameters[3DS2_enrolled]'] = 'true';
            $postData['integrity'] = 'true';
        }

        // Add customer information if available
        if (!empty($data['customer_email'])) {
            $postData['customer.email'] = $data['customer_email'];
        }

        if (!empty($data['customer_name'])) {
            $postData['customer.givenName'] = $data['customer_name'];
            $postData['customer.surname'] = $data['customer_name'];
        }

        // Add billing information if available
        if (!empty($data['billing_address'])) {
            $postData['billing.street1'] = $data['billing_address'];
        }

        if (!empty($data['billing_city'])) {
            $postData['billing.city'] = $data['billing_city'];
            $postData['billing.state'] = $data['billing_city'];
        }

        if (!empty($data['billing_country'])) {
            $postData['billing.country'] = $data['billing_country'];
        }

        if (!empty($data['billing_postcode'])) {
            $postData['billing.postcode'] = $data['billing_postcode'];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])->asForm()->post($url, $postData);

            $responseData = $response->json();

            Log::info('HyperPay Checkout Request', [
                'url' => $url,
                'data' => $postData,
                'response' => $responseData
            ]);

            if ($response->successful() && isset($responseData['result']['code']) && $responseData['result']['code'] === '000.200.100') {
                return $responseData;
            } else {
                throw new Exception('HyperPay checkout creation failed: ' . ($responseData['result']['description'] ?? 'Unknown error'));
            }
        } catch (Exception $e) {
            Log::error('HyperPay Checkout Error', [
                'error' => $e->getMessage(),
                'data' => $postData
            ]);
            throw $e;
        }
    }

    public function getPaymentStatus(string $resourcePath, string $checkoutId): array
    {
        $url = $this->getApiUrl() . $resourcePath;
        $accessToken = $this->getAccessToken();

        if (!$accessToken) {
            throw new Exception('HyperPay access token is not configured.');
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ])->get($url);

            $responseData = $response->json();

            Log::info('HyperPay Payment Status Check', [
                'url' => $url,
                'checkout_id' => $checkoutId,
                'response' => $responseData
            ]);

            return $responseData;
        } catch (Exception $e) {
            Log::error('HyperPay Payment Status Error', [
                'error' => $e->getMessage(),
                'checkout_id' => $checkoutId
            ]);
            throw $e;
        }
    }

    public function isSuccessfulPayment(string $resultCode): bool
    {
        // Success patterns from HyperPay documentation
        $successPatterns = [
            '/^(000\.000\.|000\.100\.1|000\.[36])/',  // Successful payments
        ];

        foreach ($successPatterns as $pattern) {
            if (preg_match($pattern, $resultCode)) {
                return true;
            }
        }

        return false;
    }

    public function isPendingPayment(string $resultCode): bool
    {
        // Pending patterns from HyperPay documentation
        $pendingPatterns = [
            '/^(000\.200)/',                          // Pending payments
            '/^(800\.400\.5|100\.400\.500)/',        // Pending v2 payments
        ];

        foreach ($pendingPatterns as $pattern) {
            if (preg_match($pattern, $resultCode)) {
                return true;
            }
        }

        return false;
    }

    public function setCurrency(string $currency): static
    {
        $this->currency = $currency;
        return $this;
    }

    public function setAmount(float $amount): static
    {
        $this->amount = $amount;
        return $this;
    }
}
