{"__meta": {"id": "01K2NDSB8KGECZ57D3SJJZQSG1", "datetime": "2025-08-14 23:14:05", "utime": **********.716255, "method": "GET", "uri": "/checkout/50da09dec9a537b0bbea6825c33a42fc", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 236, "start": 1755213244.324112, "end": **********.716273, "duration": 1.3921611309051514, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1755213244.324112, "relative_start": 0, "end": **********.020572, "relative_end": **********.020572, "duration": 0.****************, "duration_str": "696ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.020595, "relative_start": 0.****************, "end": **********.716275, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "696ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.038743, "relative_start": 0.****************, "end": **********.048553, "relative_end": **********.048553, "duration": 0.*****************, "duration_str": "9.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/ecommerce::orders.partials.amount", "start": **********.368125, "relative_start": 1.****************, "end": **********.368125, "relative_end": **********.368125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::orders.checkout.products", "start": **********.372114, "relative_start": 1.***************, "end": **********.372114, "relative_end": **********.372114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::themes.includes.rating-star", "start": **********.391715, "relative_start": 1.0676031112670898, "end": **********.391715, "relative_end": **********.391715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.checkout.product", "start": **********.394806, "relative_start": 1.0706939697265625, "end": **********.394806, "relative_end": **********.394806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7df89dd2efa39b7a56dc8e528ce4f9d4", "start": **********.41669, "relative_start": 1.0925781726837158, "end": **********.41669, "relative_end": **********.41669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::baaf02f6c56c328f3c307011a60b6b9f", "start": **********.420488, "relative_start": 1.0963761806488037, "end": **********.420488, "relative_end": **********.420488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::themes.includes.cart-item-options-extras", "start": **********.421463, "relative_start": 1.09735107421875, "end": **********.421463, "relative_end": **********.421463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::themes.discounts.partials.form", "start": **********.429442, "relative_start": 1.105329990386963, "end": **********.429442, "relative_end": **********.429442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::themes.discounts.partials.apply-coupon", "start": **********.430812, "relative_start": 1.1066999435424805, "end": **********.430812, "relative_end": **********.430812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.logo", "start": **********.437094, "relative_start": 1.1129820346832275, "end": **********.437094, "relative_end": **********.437094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.payment-methods", "start": **********.444899, "relative_start": 1.1207871437072754, "end": **********.444899, "relative_end": **********.444899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/stripe::methods", "start": **********.446427, "relative_start": 1.1223151683807373, "end": **********.446427, "relative_end": **********.446427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/paypal::methods", "start": **********.447359, "relative_start": 1.1232471466064453, "end": **********.447359, "relative_end": **********.447359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/hyperpay::methods", "start": **********.448155, "relative_start": 1.1240429878234863, "end": **********.448155, "relative_end": **********.448155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: fa081de1c3ee47622336b4eeafa01705::payment-method", "start": **********.454639, "relative_start": 1.1305270195007324, "end": **********.454639, "relative_end": **********.454639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/razorpay::methods", "start": **********.46123, "relative_start": 1.1371181011199951, "end": **********.46123, "relative_end": **********.46123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/cmi::methods", "start": **********.463434, "relative_start": 1.13932204246521, "end": **********.463434, "relative_end": **********.463434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/paystack::methods", "start": **********.464776, "relative_start": 1.1406641006469727, "end": **********.464776, "relative_end": **********.464776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/tabby::methods", "start": **********.481147, "relative_start": 1.1570351123809814, "end": **********.481147, "relative_end": **********.481147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: fa081de1c3ee47622336b4eeafa01705::payment-method", "start": **********.486908, "relative_start": 1.1627960205078125, "end": **********.486908, "relative_end": **********.486908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/sslcommerz::methods", "start": **********.492949, "relative_start": 1.168837070465088, "end": **********.492949, "relative_end": **********.492949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/mollie::methods", "start": **********.494092, "relative_start": 1.1699800491333008, "end": **********.494092, "relative_end": **********.494092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/payment::partials.cod", "start": **********.495163, "relative_start": 1.171051025390625, "end": **********.495163, "relative_end": **********.495163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: fa081de1c3ee47622336b4eeafa01705::payment-method", "start": **********.497655, "relative_start": 1.****************, "end": **********.497655, "relative_end": **********.497655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/payment::partials.bank-transfer", "start": **********.501758, "relative_start": 1.****************, "end": **********.501758, "relative_end": **********.501758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: fa081de1c3ee47622336b4eeafa01705::payment-method", "start": **********.503207, "relative_start": 1.****************, "end": **********.503207, "relative_end": **********.503207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.tax-information", "start": **********.521793, "relative_start": 1.***************, "end": **********.521793, "relative_end": **********.521793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.error", "start": **********.525194, "relative_start": 1.2010819911956787, "end": **********.525194, "relative_end": **********.525194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.error", "start": **********.525805, "relative_start": 1.201693058013916, "end": **********.525805, "relative_end": **********.525805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.error", "start": **********.526166, "relative_start": 1.2020540237426758, "end": **********.526166, "relative_end": **********.526166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.error", "start": **********.526495, "relative_start": 1.202383041381836, "end": **********.526495, "relative_end": **********.526495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.back-to-cart", "start": **********.531265, "relative_start": 1.207153081893921, "end": **********.531265, "relative_end": **********.531265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::68cfe903c7ebdc31f8ba9ce87e42c9a8", "start": **********.532678, "relative_start": 1.2085659503936768, "end": **********.532678, "relative_end": **********.532678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.checkout-button", "start": **********.53467, "relative_start": 1.2105581760406494, "end": **********.53467, "relative_end": **********.53467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.541699, "relative_start": 1.2175869941711426, "end": **********.713289, "relative_end": **********.713289, "duration": 0.17159008979797363, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/ecommerce::orders.checkout", "start": **********.542512, "relative_start": 1.218400001525879, "end": **********.542512, "relative_end": **********.542512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/payment::partials.header", "start": **********.543388, "relative_start": 1.219275951385498, "end": **********.543388, "relative_end": **********.543388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.597309, "relative_start": 1.2731971740722656, "end": **********.597309, "relative_end": **********.597309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.599024, "relative_start": 1.2749121189117432, "end": **********.599024, "relative_end": **********.599024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.600051, "relative_start": 1.2759389877319336, "end": **********.600051, "relative_end": **********.600051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.601116, "relative_start": 1.2770040035247803, "end": **********.601116, "relative_end": **********.601116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.60252, "relative_start": 1.2784080505371094, "end": **********.60252, "relative_end": **********.60252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.603273, "relative_start": 1.279160976409912, "end": **********.603273, "relative_end": **********.603273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.604184, "relative_start": 1.2800719738006592, "end": **********.604184, "relative_end": **********.604184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.604925, "relative_start": 1.2808129787445068, "end": **********.604925, "relative_end": **********.604925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.605302, "relative_start": 1.2811901569366455, "end": **********.605302, "relative_end": **********.605302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.605909, "relative_start": 1.281797170639038, "end": **********.605909, "relative_end": **********.605909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.606164, "relative_start": 1.2820520401000977, "end": **********.606164, "relative_end": **********.606164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.606427, "relative_start": 1.2823150157928467, "end": **********.606427, "relative_end": **********.606427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": **********.60702, "relative_start": 1.2829079627990723, "end": **********.60702, "relative_end": **********.60702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.608447, "relative_start": 1.2843351364135742, "end": **********.608447, "relative_end": **********.608447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.608981, "relative_start": 1.2848689556121826, "end": **********.608981, "relative_end": **********.608981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.609536, "relative_start": 1.285423994064331, "end": **********.609536, "relative_end": **********.609536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.610278, "relative_start": 1.2861659526824951, "end": **********.610278, "relative_end": **********.610278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.610586, "relative_start": 1.2864739894866943, "end": **********.610586, "relative_end": **********.610586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.610875, "relative_start": 1.2867629528045654, "end": **********.610875, "relative_end": **********.610875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.611243, "relative_start": 1.2871310710906982, "end": **********.611243, "relative_end": **********.611243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.611622, "relative_start": 1.2875101566314697, "end": **********.611622, "relative_end": **********.611622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.612066, "relative_start": 1.2879540920257568, "end": **********.612066, "relative_end": **********.612066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.612325, "relative_start": 1.2882130146026611, "end": **********.612325, "relative_end": **********.612325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.612574, "relative_start": 1.2884621620178223, "end": **********.612574, "relative_end": **********.612574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.612865, "relative_start": 1.2887530326843262, "end": **********.612865, "relative_end": **********.612865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.613183, "relative_start": 1.2890710830688477, "end": **********.613183, "relative_end": **********.613183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.613622, "relative_start": 1.2895100116729736, "end": **********.613622, "relative_end": **********.613622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.613836, "relative_start": 1.2897241115570068, "end": **********.613836, "relative_end": **********.613836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.614114, "relative_start": 1.2900021076202393, "end": **********.614114, "relative_end": **********.614114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.614403, "relative_start": 1.2902910709381104, "end": **********.614403, "relative_end": **********.614403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.61472, "relative_start": 1.2906081676483154, "end": **********.61472, "relative_end": **********.61472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.61514, "relative_start": 1.2910280227661133, "end": **********.61514, "relative_end": **********.61514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.615384, "relative_start": 1.2912721633911133, "end": **********.615384, "relative_end": **********.615384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.615635, "relative_start": 1.2915229797363281, "end": **********.615635, "relative_end": **********.615635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.61597, "relative_start": 1.2918579578399658, "end": **********.61597, "relative_end": **********.61597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.616322, "relative_start": 1.2922101020812988, "end": **********.616322, "relative_end": **********.616322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.616884, "relative_start": 1.2927720546722412, "end": **********.616884, "relative_end": **********.616884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.617101, "relative_start": 1.2929890155792236, "end": **********.617101, "relative_end": **********.617101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.617399, "relative_start": 1.2932870388031006, "end": **********.617399, "relative_end": **********.617399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.617706, "relative_start": 1.2935941219329834, "end": **********.617706, "relative_end": **********.617706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.618036, "relative_start": 1.29392409324646, "end": **********.618036, "relative_end": **********.618036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.618532, "relative_start": 1.2944200038909912, "end": **********.618532, "relative_end": **********.618532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.618757, "relative_start": 1.294645071029663, "end": **********.618757, "relative_end": **********.618757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.618997, "relative_start": 1.2948851585388184, "end": **********.618997, "relative_end": **********.618997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.619384, "relative_start": 1.2952721118927002, "end": **********.619384, "relative_end": **********.619384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.619789, "relative_start": 1.2956769466400146, "end": **********.619789, "relative_end": **********.619789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.620252, "relative_start": 1.2961399555206299, "end": **********.620252, "relative_end": **********.620252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.620475, "relative_start": 1.296363115310669, "end": **********.620475, "relative_end": **********.620475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.620743, "relative_start": 1.296631097793579, "end": **********.620743, "relative_end": **********.620743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.621115, "relative_start": 1.2970030307769775, "end": **********.621115, "relative_end": **********.621115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.621515, "relative_start": 1.29740309715271, "end": **********.621515, "relative_end": **********.621515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.62197, "relative_start": 1.2978579998016357, "end": **********.62197, "relative_end": **********.62197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.622239, "relative_start": 1.2981271743774414, "end": **********.622239, "relative_end": **********.622239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.622494, "relative_start": 1.298382043838501, "end": **********.622494, "relative_end": **********.622494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.622801, "relative_start": 1.2986891269683838, "end": **********.622801, "relative_end": **********.622801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.623138, "relative_start": 1.2990260124206543, "end": **********.623138, "relative_end": **********.623138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.623642, "relative_start": 1.299530029296875, "end": **********.623642, "relative_end": **********.623642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.623872, "relative_start": 1.299760103225708, "end": **********.623872, "relative_end": **********.623872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.624156, "relative_start": 1.300044059753418, "end": **********.624156, "relative_end": **********.624156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.624458, "relative_start": 1.3003461360931396, "end": **********.624458, "relative_end": **********.624458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.624781, "relative_start": 1.3006689548492432, "end": **********.624781, "relative_end": **********.624781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.625223, "relative_start": 1.3011109828948975, "end": **********.625223, "relative_end": **********.625223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.625442, "relative_start": 1.3013300895690918, "end": **********.625442, "relative_end": **********.625442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.625703, "relative_start": 1.301591157913208, "end": **********.625703, "relative_end": **********.625703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.626051, "relative_start": 1.3019390106201172, "end": **********.626051, "relative_end": **********.626051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.626401, "relative_start": 1.3022890090942383, "end": **********.626401, "relative_end": **********.626401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.626847, "relative_start": 1.3027350902557373, "end": **********.626847, "relative_end": **********.626847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.627102, "relative_start": 1.3029899597167969, "end": **********.627102, "relative_end": **********.627102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.627404, "relative_start": 1.3032920360565186, "end": **********.627404, "relative_end": **********.627404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.627728, "relative_start": 1.3036160469055176, "end": **********.627728, "relative_end": **********.627728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.628037, "relative_start": 1.3039250373840332, "end": **********.628037, "relative_end": **********.628037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.628455, "relative_start": 1.3043429851531982, "end": **********.628455, "relative_end": **********.628455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.628663, "relative_start": 1.304551124572754, "end": **********.628663, "relative_end": **********.628663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.628898, "relative_start": 1.304785966873169, "end": **********.628898, "relative_end": **********.628898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.629198, "relative_start": 1.3050861358642578, "end": **********.629198, "relative_end": **********.629198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.629603, "relative_start": 1.3054909706115723, "end": **********.629603, "relative_end": **********.629603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.630084, "relative_start": 1.3059720993041992, "end": **********.630084, "relative_end": **********.630084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.630332, "relative_start": 1.3062200546264648, "end": **********.630332, "relative_end": **********.630332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.630569, "relative_start": 1.3064570426940918, "end": **********.630569, "relative_end": **********.630569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.630844, "relative_start": 1.306732177734375, "end": **********.630844, "relative_end": **********.630844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.631151, "relative_start": 1.3070390224456787, "end": **********.631151, "relative_end": **********.631151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.631595, "relative_start": 1.3074829578399658, "end": **********.631595, "relative_end": **********.631595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.631834, "relative_start": 1.3077220916748047, "end": **********.631834, "relative_end": **********.631834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.632228, "relative_start": 1.3081159591674805, "end": **********.632228, "relative_end": **********.632228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.632566, "relative_start": 1.3084540367126465, "end": **********.632566, "relative_end": **********.632566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.632952, "relative_start": 1.308840036392212, "end": **********.632952, "relative_end": **********.632952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.633414, "relative_start": 1.3093020915985107, "end": **********.633414, "relative_end": **********.633414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.633645, "relative_start": 1.3095331192016602, "end": **********.633645, "relative_end": **********.633645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.6339, "relative_start": 1.3097879886627197, "end": **********.6339, "relative_end": **********.6339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": **********.634221, "relative_start": 1.3101091384887695, "end": **********.634221, "relative_end": **********.634221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.634846, "relative_start": 1.3107340335845947, "end": **********.634846, "relative_end": **********.634846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.635195, "relative_start": 1.3110830783843994, "end": **********.635195, "relative_end": **********.635195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.635632, "relative_start": 1.3115200996398926, "end": **********.635632, "relative_end": **********.635632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.636258, "relative_start": 1.3121459484100342, "end": **********.636258, "relative_end": **********.636258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.636522, "relative_start": 1.3124101161956787, "end": **********.636522, "relative_end": **********.636522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.636855, "relative_start": 1.3127429485321045, "end": **********.636855, "relative_end": **********.636855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.63717, "relative_start": 1.3130581378936768, "end": **********.63717, "relative_end": **********.63717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.637598, "relative_start": 1.313486099243164, "end": **********.637598, "relative_end": **********.637598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.638059, "relative_start": 1.3139469623565674, "end": **********.638059, "relative_end": **********.638059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.63831, "relative_start": 1.3141980171203613, "end": **********.63831, "relative_end": **********.63831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.638585, "relative_start": 1.3144731521606445, "end": **********.638585, "relative_end": **********.638585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.638925, "relative_start": 1.3148131370544434, "end": **********.638925, "relative_end": **********.638925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.639289, "relative_start": 1.3151769638061523, "end": **********.639289, "relative_end": **********.639289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.639725, "relative_start": 1.315613031387329, "end": **********.639725, "relative_end": **********.639725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.639938, "relative_start": 1.315826177597046, "end": **********.639938, "relative_end": **********.639938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.640214, "relative_start": 1.3161020278930664, "end": **********.640214, "relative_end": **********.640214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.640503, "relative_start": 1.3163909912109375, "end": **********.640503, "relative_end": **********.640503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.64084, "relative_start": 1.316728115081787, "end": **********.64084, "relative_end": **********.64084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.641259, "relative_start": 1.3171470165252686, "end": **********.641259, "relative_end": **********.641259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.641482, "relative_start": 1.3173701763153076, "end": **********.641482, "relative_end": **********.641482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.641735, "relative_start": 1.3176231384277344, "end": **********.641735, "relative_end": **********.641735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.642187, "relative_start": 1.318075180053711, "end": **********.642187, "relative_end": **********.642187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.643029, "relative_start": 1.3189170360565186, "end": **********.643029, "relative_end": **********.643029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.643767, "relative_start": 1.319655179977417, "end": **********.643767, "relative_end": **********.643767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.644486, "relative_start": 1.3203740119934082, "end": **********.644486, "relative_end": **********.644486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.644929, "relative_start": 1.320816993713379, "end": **********.644929, "relative_end": **********.644929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.645141, "relative_start": 1.3210289478302002, "end": **********.645141, "relative_end": **********.645141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.645386, "relative_start": 1.3212740421295166, "end": **********.645386, "relative_end": **********.645386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.645681, "relative_start": 1.3215689659118652, "end": **********.645681, "relative_end": **********.645681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.646027, "relative_start": 1.3219151496887207, "end": **********.646027, "relative_end": **********.646027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.646448, "relative_start": 1.322335958480835, "end": **********.646448, "relative_end": **********.646448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.646659, "relative_start": 1.3225469589233398, "end": **********.646659, "relative_end": **********.646659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.646899, "relative_start": 1.3227870464324951, "end": **********.646899, "relative_end": **********.646899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.647181, "relative_start": 1.3230690956115723, "end": **********.647181, "relative_end": **********.647181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.647504, "relative_start": 1.3233921527862549, "end": **********.647504, "relative_end": **********.647504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.648018, "relative_start": 1.3239059448242188, "end": **********.648018, "relative_end": **********.648018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.648268, "relative_start": 1.3241560459136963, "end": **********.648268, "relative_end": **********.648268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.648543, "relative_start": 1.3244309425354004, "end": **********.648543, "relative_end": **********.648543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.648854, "relative_start": 1.324742078781128, "end": **********.648854, "relative_end": **********.648854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.64917, "relative_start": 1.3250579833984375, "end": **********.64917, "relative_end": **********.64917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.649606, "relative_start": 1.3254940509796143, "end": **********.649606, "relative_end": **********.649606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.649819, "relative_start": 1.325706958770752, "end": **********.649819, "relative_end": **********.649819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.650061, "relative_start": 1.32594895362854, "end": **********.650061, "relative_end": **********.650061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.650371, "relative_start": 1.3262591361999512, "end": **********.650371, "relative_end": **********.650371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.650773, "relative_start": 1.3266611099243164, "end": **********.650773, "relative_end": **********.650773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.651211, "relative_start": 1.327099084854126, "end": **********.651211, "relative_end": **********.651211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.651436, "relative_start": 1.3273241519927979, "end": **********.651436, "relative_end": **********.651436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.651691, "relative_start": 1.3275790214538574, "end": **********.651691, "relative_end": **********.651691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.651976, "relative_start": 1.327864170074463, "end": **********.651976, "relative_end": **********.651976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.65232, "relative_start": 1.3282079696655273, "end": **********.65232, "relative_end": **********.65232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.652744, "relative_start": 1.328632116317749, "end": **********.652744, "relative_end": **********.652744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.652956, "relative_start": 1.3288440704345703, "end": **********.652956, "relative_end": **********.652956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.653214, "relative_start": 1.3291020393371582, "end": **********.653214, "relative_end": **********.653214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.653622, "relative_start": 1.329509973526001, "end": **********.653622, "relative_end": **********.653622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.654045, "relative_start": 1.3299331665039062, "end": **********.654045, "relative_end": **********.654045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.654487, "relative_start": 1.3303749561309814, "end": **********.654487, "relative_end": **********.654487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.654753, "relative_start": 1.3306410312652588, "end": **********.654753, "relative_end": **********.654753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.655003, "relative_start": 1.3308911323547363, "end": **********.655003, "relative_end": **********.655003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.655293, "relative_start": 1.3311810493469238, "end": **********.655293, "relative_end": **********.655293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.655616, "relative_start": 1.3315041065216064, "end": **********.655616, "relative_end": **********.655616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.656066, "relative_start": 1.331954002380371, "end": **********.656066, "relative_end": **********.656066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.656282, "relative_start": 1.332170009613037, "end": **********.656282, "relative_end": **********.656282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.656524, "relative_start": 1.3324120044708252, "end": **********.656524, "relative_end": **********.656524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.656809, "relative_start": 1.3326971530914307, "end": **********.656809, "relative_end": **********.656809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.657121, "relative_start": 1.3330090045928955, "end": **********.657121, "relative_end": **********.657121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.657539, "relative_start": 1.3334269523620605, "end": **********.657539, "relative_end": **********.657539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.65778, "relative_start": 1.3336679935455322, "end": **********.65778, "relative_end": **********.65778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.658048, "relative_start": 1.3339359760284424, "end": **********.658048, "relative_end": **********.658048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.658333, "relative_start": 1.3342211246490479, "end": **********.658333, "relative_end": **********.658333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.658643, "relative_start": 1.3345310688018799, "end": **********.658643, "relative_end": **********.658643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.659059, "relative_start": 1.334947109222412, "end": **********.659059, "relative_end": **********.659059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.659281, "relative_start": 1.3351690769195557, "end": **********.659281, "relative_end": **********.659281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.65952, "relative_start": 1.3354079723358154, "end": **********.65952, "relative_end": **********.65952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.659801, "relative_start": 1.3356890678405762, "end": **********.659801, "relative_end": **********.659801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.660113, "relative_start": 1.3360011577606201, "end": **********.660113, "relative_end": **********.660113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.660531, "relative_start": 1.3364191055297852, "end": **********.660531, "relative_end": **********.660531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.660743, "relative_start": 1.3366310596466064, "end": **********.660743, "relative_end": **********.660743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.66098, "relative_start": 1.3368680477142334, "end": **********.66098, "relative_end": **********.66098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.66126, "relative_start": 1.3371479511260986, "end": **********.66126, "relative_end": **********.66126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.66157, "relative_start": 1.3374581336975098, "end": **********.66157, "relative_end": **********.66157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.662002, "relative_start": 1.3378901481628418, "end": **********.662002, "relative_end": **********.662002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.662215, "relative_start": 1.3381030559539795, "end": **********.662215, "relative_end": **********.662215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.662451, "relative_start": 1.33833909034729, "end": **********.662451, "relative_end": **********.662451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.662878, "relative_start": 1.338766098022461, "end": **********.662878, "relative_end": **********.662878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.663415, "relative_start": 1.3393030166625977, "end": **********.663415, "relative_end": **********.663415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.663865, "relative_start": 1.3397531509399414, "end": **********.663865, "relative_end": **********.663865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.664104, "relative_start": 1.3399920463562012, "end": **********.664104, "relative_end": **********.664104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.664349, "relative_start": 1.3402371406555176, "end": **********.664349, "relative_end": **********.664349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.664634, "relative_start": 1.340522050857544, "end": **********.664634, "relative_end": **********.664634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.664943, "relative_start": 1.3408310413360596, "end": **********.664943, "relative_end": **********.664943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.665382, "relative_start": 1.3412699699401855, "end": **********.665382, "relative_end": **********.665382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.665592, "relative_start": 1.341480016708374, "end": **********.665592, "relative_end": **********.665592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.665828, "relative_start": 1.3417160511016846, "end": **********.665828, "relative_end": **********.665828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.666146, "relative_start": 1.342034101486206, "end": **********.666146, "relative_end": **********.666146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.666455, "relative_start": 1.3423430919647217, "end": **********.666455, "relative_end": **********.666455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.666871, "relative_start": 1.342759132385254, "end": **********.666871, "relative_end": **********.666871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.667079, "relative_start": 1.3429670333862305, "end": **********.667079, "relative_end": **********.667079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.667329, "relative_start": 1.343217134475708, "end": **********.667329, "relative_end": **********.667329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.667618, "relative_start": 1.343506097793579, "end": **********.667618, "relative_end": **********.667618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.667943, "relative_start": 1.3438310623168945, "end": **********.667943, "relative_end": **********.667943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.668372, "relative_start": 1.3442599773406982, "end": **********.668372, "relative_end": **********.668372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.668596, "relative_start": 1.3444840908050537, "end": **********.668596, "relative_end": **********.668596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.668834, "relative_start": 1.344722032546997, "end": **********.668834, "relative_end": **********.668834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.696196, "relative_start": 1.372084140777588, "end": **********.696196, "relative_end": **********.696196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/payment::partials.footer", "start": **********.697091, "relative_start": 1.3729791641235352, "end": **********.697091, "relative_end": **********.697091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.master", "start": **********.700325, "relative_start": 1.3762130737304688, "end": **********.700325, "relative_end": **********.700325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 61486448, "peak_usage_str": "59MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 232, "nb_templates": 232, "templates": [{"name": "1x plugins/ecommerce::orders.partials.amount", "param_count": null, "params": [], "start": **********.368098, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/amount.blade.phpplugins/ecommerce::orders.partials.amount", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Famount.blade.php&line=1", "ajax": false, "filename": "amount.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.partials.amount"}, {"name": "1x plugins/marketplace::orders.checkout.products", "param_count": null, "params": [], "start": **********.372091, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.phpplugins/marketplace::orders.checkout.products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproducts.blade.php&line=1", "ajax": false, "filename": "products.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::orders.checkout.products"}, {"name": "1x plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": **********.391692, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::themes.includes.rating-star"}, {"name": "1x plugins/ecommerce::orders.checkout.product", "param_count": null, "params": [], "start": **********.394783, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/checkout/product.blade.phpplugins/ecommerce::orders.checkout.product", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproduct.blade.php&line=1", "ajax": false, "filename": "product.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.checkout.product"}, {"name": "1x __components::7df89dd2efa39b7a56dc8e528ce4f9d4", "param_count": null, "params": [], "start": **********.416664, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7df89dd2efa39b7a56dc8e528ce4f9d4.blade.php__components::7df89dd2efa39b7a56dc8e528ce4f9d4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7df89dd2efa39b7a56dc8e528ce4f9d4.blade.php&line=1", "ajax": false, "filename": "7df89dd2efa39b7a56dc8e528ce4f9d4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7df89dd2efa39b7a56dc8e528ce4f9d4"}, {"name": "1x __components::baaf02f6c56c328f3c307011a60b6b9f", "param_count": null, "params": [], "start": **********.420463, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/baaf02f6c56c328f3c307011a60b6b9f.blade.php__components::baaf02f6c56c328f3c307011a60b6b9f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fbaaf02f6c56c328f3c307011a60b6b9f.blade.php&line=1", "ajax": false, "filename": "baaf02f6c56c328f3c307011a60b6b9f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::baaf02f6c56c328f3c307011a60b6b9f"}, {"name": "1x plugins/ecommerce::themes.includes.cart-item-options-extras", "param_count": null, "params": [], "start": **********.42144, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/themes/includes/cart-item-options-extras.blade.phpplugins/ecommerce::themes.includes.cart-item-options-extras", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fcart-item-options-extras.blade.php&line=1", "ajax": false, "filename": "cart-item-options-extras.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::themes.includes.cart-item-options-extras"}, {"name": "1x plugins/ecommerce::themes.discounts.partials.form", "param_count": null, "params": [], "start": **********.429421, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/themes/discounts/partials/form.blade.phpplugins/ecommerce::themes.discounts.partials.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fdiscounts%2Fpartials%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::themes.discounts.partials.form"}, {"name": "1x plugins/ecommerce::themes.discounts.partials.apply-coupon", "param_count": null, "params": [], "start": **********.430791, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/themes/discounts/partials/apply-coupon.blade.phpplugins/ecommerce::themes.discounts.partials.apply-coupon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fdiscounts%2Fpartials%2Fapply-coupon.blade.php&line=1", "ajax": false, "filename": "apply-coupon.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::themes.discounts.partials.apply-coupon"}, {"name": "1x plugins/ecommerce::orders.partials.logo", "param_count": null, "params": [], "start": **********.437068, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/logo.blade.phpplugins/ecommerce::orders.partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.partials.logo"}, {"name": "1x plugins/ecommerce::orders.partials.payment-methods", "param_count": null, "params": [], "start": **********.444874, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/payment-methods.blade.phpplugins/ecommerce::orders.partials.payment-methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fpayment-methods.blade.php&line=1", "ajax": false, "filename": "payment-methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.partials.payment-methods"}, {"name": "1x plugins/stripe::methods", "param_count": null, "params": [], "start": **********.446406, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/stripe/resources/views/methods.blade.phpplugins/stripe::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fstripe%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/stripe::methods"}, {"name": "1x plugins/paypal::methods", "param_count": null, "params": [], "start": **********.447336, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/paypal/resources/views/methods.blade.phpplugins/paypal::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpaypal%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/paypal::methods"}, {"name": "1x plugins/hyperpay::methods", "param_count": null, "params": [], "start": **********.448134, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/hyperpay/resources/views/methods.blade.phpplugins/hyperpay::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fhyperpay%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/hyperpay::methods"}, {"name": "4x fa081de1c3ee47622336b4eeafa01705::payment-method", "param_count": null, "params": [], "start": **********.454613, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/components/payment-method.blade.phpfa081de1c3ee47622336b4eeafa01705::payment-method", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fcomponents%2Fpayment-method.blade.php&line=1", "ajax": false, "filename": "payment-method.blade.php", "line": "?"}, "render_count": 4, "name_original": "fa081de1c3ee47622336b4eeafa01705::payment-method"}, {"name": "1x plugins/razorpay::methods", "param_count": null, "params": [], "start": **********.461206, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/razorpay/resources/views/methods.blade.phpplugins/razorpay::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Frazorpay%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/razorpay::methods"}, {"name": "1x plugins/cmi::methods", "param_count": null, "params": [], "start": **********.463413, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/cmi/resources/views/methods.blade.phpplugins/cmi::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fcmi%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/cmi::methods"}, {"name": "1x plugins/paystack::methods", "param_count": null, "params": [], "start": **********.464756, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/paystack/resources/views/methods.blade.phpplugins/paystack::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpaystack%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/paystack::methods"}, {"name": "1x plugins/tabby::methods", "param_count": null, "params": [], "start": **********.481122, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/tabby/resources/views/methods.blade.phpplugins/tabby::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ftabby%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/tabby::methods"}, {"name": "1x plugins/sslcommerz::methods", "param_count": null, "params": [], "start": **********.492927, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/sslcommerz/resources/views/methods.blade.phpplugins/sslcommerz::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fsslcommerz%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/sslcommerz::methods"}, {"name": "1x plugins/mollie::methods", "param_count": null, "params": [], "start": **********.494071, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/mollie/resources/views/methods.blade.phpplugins/mollie::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmollie%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/mollie::methods"}, {"name": "1x plugins/payment::partials.cod", "param_count": null, "params": [], "start": **********.495143, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/partials/cod.blade.phpplugins/payment::partials.cod", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Fcod.blade.php&line=1", "ajax": false, "filename": "cod.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/payment::partials.cod"}, {"name": "1x plugins/payment::partials.bank-transfer", "param_count": null, "params": [], "start": **********.501738, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/partials/bank-transfer.blade.phpplugins/payment::partials.bank-transfer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Fbank-transfer.blade.php&line=1", "ajax": false, "filename": "bank-transfer.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/payment::partials.bank-transfer"}, {"name": "1x plugins/ecommerce::orders.partials.tax-information", "param_count": null, "params": [], "start": **********.52174, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/tax-information.blade.phpplugins/ecommerce::orders.partials.tax-information", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Ftax-information.blade.php&line=1", "ajax": false, "filename": "tax-information.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.partials.tax-information"}, {"name": "4x core/base::forms.partials.error", "param_count": null, "params": [], "start": **********.525156, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/error.blade.phpcore/base::forms.partials.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.partials.error"}, {"name": "1x plugins/ecommerce::orders.partials.back-to-cart", "param_count": null, "params": [], "start": **********.53123, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/back-to-cart.blade.phpplugins/ecommerce::orders.partials.back-to-cart", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fback-to-cart.blade.php&line=1", "ajax": false, "filename": "back-to-cart.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.partials.back-to-cart"}, {"name": "1x __components::68cfe903c7ebdc31f8ba9ce87e42c9a8", "param_count": null, "params": [], "start": **********.532656, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/68cfe903c7ebdc31f8ba9ce87e42c9a8.blade.php__components::68cfe903c7ebdc31f8ba9ce87e42c9a8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F68cfe903c7ebdc31f8ba9ce87e42c9a8.blade.php&line=1", "ajax": false, "filename": "68cfe903c7ebdc31f8ba9ce87e42c9a8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::68cfe903c7ebdc31f8ba9ce87e42c9a8"}, {"name": "1x plugins/ecommerce::orders.partials.checkout-button", "param_count": null, "params": [], "start": **********.534628, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/checkout-button.blade.phpplugins/ecommerce::orders.partials.checkout-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fcheckout-button.blade.php&line=1", "ajax": false, "filename": "checkout-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.partials.checkout-button"}, {"name": "1x plugins/ecommerce::orders.checkout", "param_count": null, "params": [], "start": **********.542487, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/checkout.blade.phpplugins/ecommerce::orders.checkout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fcheckout.blade.php&line=1", "ajax": false, "filename": "checkout.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.checkout"}, {"name": "1x plugins/payment::partials.header", "param_count": null, "params": [], "start": **********.543367, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/partials/header.blade.phpplugins/payment::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/payment::partials.header"}, {"name": "2x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.597285, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.599001, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php&line=1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "36x core/base::forms.fields.html", "param_count": null, "params": [], "start": **********.600029, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php&line=1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 36, "name_original": "core/base::forms.fields.html"}, {"name": "37x 8def1252668913628243c4d363bee1ef::form.field", "param_count": null, "params": [], "start": **********.601079, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/field.blade.php8def1252668913628243c4d363bee1ef::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 37, "name_original": "8def1252668913628243c4d363bee1ef::form.field"}, {"name": "37x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.602476, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 37, "name_original": "core/base::forms.partials.help-block"}, {"name": "37x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.603239, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 37, "name_original": "core/base::forms.partials.errors"}, {"name": "39x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.604146, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 39, "name_original": "core/base::forms.columns.column-span"}, {"name": "2x laravel-form-builder::text", "param_count": null, "params": [], "start": **********.607001, "type": "php", "hash": "phpD:\\laragon\\www\\martfury\\vendor\\botble\\form-builder\\src/../resources/views/text.phplaravel-form-builder::text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fform-builder%2Fresources%2Fviews%2Ftext.php&line=1", "ajax": false, "filename": "text.php", "line": "?"}, "render_count": 2, "name_original": "laravel-form-builder::text"}, {"name": "1x core/base::forms.fields.textarea", "param_count": null, "params": [], "start": **********.642168, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/fields/textarea.blade.phpcore/base::forms.fields.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.textarea"}, {"name": "1x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.643009, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.label"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.label", "param_count": null, "params": [], "start": **********.643747, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/label.blade.php8def1252668913628243c4d363bee1ef::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.label"}, {"name": "1x plugins/payment::partials.footer", "param_count": null, "params": [], "start": **********.697069, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/partials/footer.blade.phpplugins/payment::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/payment::partials.footer"}, {"name": "1x plugins/ecommerce::orders.master", "param_count": null, "params": [], "start": **********.700303, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/master.blade.phpplugins/ecommerce::orders.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.master"}]}, "queries": {"count": 68, "nb_statements": 68, "nb_visible_statements": 68, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0485, "accumulated_duration_str": "48.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.0652542, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 0.928}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.084451, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.928, "width_percent": 0.825}, {"sql": "select distinct `ec_products`.*, `products_with_final_price`.`final_price`, `ec_products`.`with_storehouse_management`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`allow_checkout_when_out_of_stock` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-08-14 23:14:05' OR\nec_products.end_date < '2025-08-14 23:14:05'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-14 23:14:05' AND\nec_products.end_date >= '2025-08-14 23:14:05'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-08-14 23:14:05'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-14 23:14:05' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `status` = 'published' and `ec_products`.`id` in (24) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by\nCASE\nWHEN ec_products.with_storehouse_management = 0 THEN\nCASE WHEN ec_products.stock_status = 'out_of_stock' THEN 1 ELSE 0 END\nELSE\nCASE WHEN ec_products.quantity <= 0 AND ec_products.allow_checkout_when_out_of_stock = 0 THEN 1 ELSE 0 END\nEND ASC\n, `order` asc, `created_at` desc", "type": "query", "params": [], "bindings": ["published", 24, 1, "published", "out_of_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 605}], "start": **********.0977209, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 1.753, "width_percent": 3.423}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (24) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.105494, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 5.175, "width_percent": 0.99}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (24)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.1095018, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 6.165, "width_percent": 0.825}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 605}], "start": **********.117762, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 6.99, "width_percent": 1.299}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 605}], "start": **********.120371, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 8.289, "width_percent": 1.072}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.123143, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 9.361, "width_percent": 1.01}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (1) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}], "start": **********.126266, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 10.371, "width_percent": 1.381}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}], "start": **********.127962, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 11.753, "width_percent": 0.66}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}], "start": **********.12954, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 12.412, "width_percent": 0.701}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1) and `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store'", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 39, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}], "start": **********.130717, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 13.113, "width_percent": 0.763}, {"sql": "select distinct `ec_product_variations`.`product_id`, `ec_product_variations`.`configurable_product_id`, `ec_product_attributes`.*, `ec_product_attribute_sets`.`title` as `attribute_set_title`, `ec_product_attribute_sets`.`slug` as `attribute_set_slug`, `ec_product_attribute_sets`.`order` as `attribute_set_order` from `ec_product_variations` inner join `ec_product_variation_items` on `ec_product_variation_items`.`variation_id` = `ec_product_variations`.`id` inner join `ec_product_attributes` on `ec_product_attributes`.`id` = `ec_product_variation_items`.`attribute_id` inner join `ec_product_attribute_sets` on `ec_product_attribute_sets`.`id` = `ec_product_attributes`.`attribute_set_id` where `ec_product_variations`.`product_id` in (24) order by `attribute_set_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.134486, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 13.876, "width_percent": 0.866}, {"sql": "select * from `ec_customers` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 979}], "start": **********.1409779, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 14.742, "width_percent": 0.825}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 210}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 668}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 23}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 138}], "start": **********.142825, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:597", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=597", "ajax": false, "filename": "EcommerceHelper.php", "line": "597"}, "connection": "martfury", "explain": null, "start_percent": 15.567, "width_percent": 0.536}, {"sql": "select `ec_taxes`.*, `ec_tax_products`.`product_id` as `pivot_product_id`, `ec_tax_products`.`tax_id` as `pivot_tax_id` from `ec_taxes` inner join `ec_tax_products` on `ec_taxes`.`id` = `ec_tax_products`.`tax_id` where `ec_tax_products`.`product_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/TaxRateCalculatorService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\TaxRateCalculatorService.php", "line": 19}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 56}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 43}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 138}], "start": **********.146354, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "martfury", "explain": null, "start_percent": 16.103, "width_percent": 0.722}, {"sql": "select * from `ec_tax_rules` where `ec_tax_rules`.`tax_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 26, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/TaxRateCalculatorService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\TaxRateCalculatorService.php", "line": 19}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 56}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 43}], "start": **********.149475, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 16.825, "width_percent": 0.598}, {"sql": "select distinct `ec_products`.*, `ec_products`.`with_storehouse_management`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`allow_checkout_when_out_of_stock`, (select count(*) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_count`, (select avg(`ec_reviews`.`star`) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_avg` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-08-14 23:14:05' OR\nec_products.end_date < '2025-08-14 23:14:05'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-14 23:14:05' AND\nec_products.end_date >= '2025-08-14 23:14:05'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-08-14 23:14:05'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-14 23:14:05' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `status` = 'published' and `ec_products`.`id` in (24) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by\nCASE\nWHEN ec_products.with_storehouse_management = 0 THEN\nCASE WHEN ec_products.stock_status = 'out_of_stock' THEN 1 ELSE 0 END\nELSE\nCASE WHEN ec_products.quantity <= 0 AND ec_products.allow_checkout_when_out_of_stock = 0 THEN 1 ELSE 0 END\nEND ASC\n, `ec_products`.`order` asc, `ec_products`.`created_at` desc", "type": "query", "params": [], "bindings": ["published", "published", "published", 24, 1, "published", "out_of_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.157014, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 17.423, "width_percent": 3.711}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (24) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.160016, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 21.134, "width_percent": 0.66}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (24)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.162055, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 21.794, "width_percent": 0.598}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.16377, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 22.392, "width_percent": 0.66}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.165198, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 23.052, "width_percent": 0.639}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 24 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 730}], "start": **********.1674511, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 23.691, "width_percent": 0.598}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 1 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [1, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 730}], "start": **********.171138, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 24.289, "width_percent": 1.299}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-08-14' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-08-14", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.179573, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 25.588, "width_percent": 0.722}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-08-14 23:14:05' and (`end_date` is null or `end_date` >= '2025-08-14 23:14:05') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-08-14 23:14:05", "2025-08-14 23:14:05", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.1883779, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 26.309, "width_percent": 2.33}, {"sql": "select distinct `ec_products`.*, `ec_products`.`with_storehouse_management`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`allow_checkout_when_out_of_stock`, (select count(*) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_count`, (select avg(`ec_reviews`.`star`) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_avg` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-08-14 23:14:05' OR\nec_products.end_date < '2025-08-14 23:14:05'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-14 23:14:05' AND\nec_products.end_date >= '2025-08-14 23:14:05'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-08-14 23:14:05'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-14 23:14:05' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `ec_products`.`id` in (24) and `ec_products`.`status` = 'published' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by\nCASE\nWHEN ec_products.with_storehouse_management = 0 THEN\nCASE WHEN ec_products.stock_status = 'out_of_stock' THEN 1 ELSE 0 END\nELSE\nCASE WHEN ec_products.quantity <= 0 AND ec_products.allow_checkout_when_out_of_stock = 0 THEN 1 ELSE 0 END\nEND ASC\n, `ec_products`.`order` asc, `ec_products`.`created_at` desc", "type": "query", "params": [], "bindings": ["published", "published", 24, "published", 1, "published", "out_of_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.196882, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 28.639, "width_percent": 3.237}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (24) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.199639, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 31.876, "width_percent": 0.66}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (24)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.2018652, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 32.536, "width_percent": 0.577}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.20373, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 33.113, "width_percent": 0.68}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.205243, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 33.794, "width_percent": 0.639}, {"sql": "select `ec_products`.*, `ec_product_cross_sale_relations`.`from_product_id` as `pivot_from_product_id`, `ec_product_cross_sale_relations`.`to_product_id` as `pivot_to_product_id`, `ec_product_cross_sale_relations`.`price` as `pivot_price`, `ec_product_cross_sale_relations`.`price_type` as `pivot_price_type`, `ec_product_cross_sale_relations`.`apply_to_all_variations` as `pivot_apply_to_all_variations`, `ec_product_cross_sale_relations`.`is_variant` as `pivot_is_variant` from `ec_products` inner join `ec_product_cross_sale_relations` on `ec_products`.`id` = `ec_product_cross_sale_relations`.`to_product_id` where `ec_product_cross_sale_relations`.`from_product_id` in (24) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.207943, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 34.433, "width_percent": 0.99}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.2097142, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 35.423, "width_percent": 0.577}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (1) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}], "start": **********.2118628, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 36, "width_percent": 0.845}, {"sql": "select `ec_products`.*, `ec_product_cross_sale_relations`.`from_product_id` as `pivot_from_product_id`, `ec_product_cross_sale_relations`.`to_product_id` as `pivot_to_product_id`, `ec_product_cross_sale_relations`.`price` as `pivot_price`, `ec_product_cross_sale_relations`.`price_type` as `pivot_price_type`, `ec_product_cross_sale_relations`.`apply_to_all_variations` as `pivot_apply_to_all_variations`, `ec_product_cross_sale_relations`.`is_variant` as `pivot_is_variant` from `ec_products` inner join `ec_product_cross_sale_relations` on `ec_products`.`id` = `ec_product_cross_sale_relations`.`to_product_id` where `ec_product_cross_sale_relations`.`from_product_id` in (1) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 17}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 366}], "start": **********.216056, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:17", "source": {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=17", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "17"}, "connection": "martfury", "explain": null, "start_percent": 36.845, "width_percent": 1.072}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 2 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 366}], "start": **********.220903, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 37.918, "width_percent": 1.093}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 4 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 366}], "start": **********.224155, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 39.01, "width_percent": 2.082}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.227838, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 41.093, "width_percent": 1.113}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (30, 31, 32, 33, 34) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.232342, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 42.206, "width_percent": 1.361}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 7 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 366}], "start": **********.236131, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 43.567, "width_percent": 0.742}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.2376108, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 44.309, "width_percent": 0.515}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (39) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.240863, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 44.825, "width_percent": 1.031}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 9 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 366}], "start": **********.243067, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 45.856, "width_percent": 0.577}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (9)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.244403, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 46.433, "width_percent": 0.515}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (42) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.2472298, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 46.948, "width_percent": 0.845}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 20 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 366}], "start": **********.249357, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 47.794, "width_percent": 0.495}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.250674, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 48.289, "width_percent": 0.557}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (66, 67, 68) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.253649, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 48.845, "width_percent": 1.113}, {"sql": "select * from `ec_orders` where `id` in (46)", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1168}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 140}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.258532, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 49.959, "width_percent": 1.732}, {"sql": "select * from `ec_order_addresses` where `type` = 'shipping_address' and `ec_order_addresses`.`order_id` in (46)", "type": "query", "params": [], "bindings": ["shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1168}, {"index": 26, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 140}], "start": **********.261998, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 51.691, "width_percent": 0.68}, {"sql": "select * from `ec_customer_addresses` where `ec_customer_addresses`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 738}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1268}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.267168, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 52.371, "width_percent": 0.68}, {"sql": "delete from `ec_order_addresses` where (`order_id` = 46 and `type` = 'shipping_address')", "type": "query", "params": [], "bindings": [46, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 788}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 772}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1268}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.2709801, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:788", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 788}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=788", "ajax": false, "filename": "OrderHelper.php", "line": "788"}, "connection": "martfury", "explain": null, "start_percent": 53.052, "width_percent": 2.124}, {"sql": "update `ec_orders` set `shipping_method` = 'default', `amount` = 353.1, `tax_amount` = 32.1, `payment_fee` = 0, `sub_total` = 321, `ec_orders`.`updated_at` = '2025-08-14 23:14:05' where `id` = 46", "type": "query", "params": [], "bindings": [{"value": "default", "label": "<PERSON><PERSON><PERSON>"}, 353.1, 32.1, 0, 321, "2025-08-14 23:14:05", 46], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1271}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}, {"index": 19, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 140}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.27805, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "OrderSupportServiceProvider.php:1271", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1271}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FProviders%2FOrderSupportServiceProvider.php&line=1271", "ajax": false, "filename": "OrderSupportServiceProvider.php", "line": "1271"}, "connection": "martfury", "explain": null, "start_percent": 55.175, "width_percent": 7.959}, {"sql": "select * from `ec_order_product` where `order_id` = 46", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 934}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1273}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.2908719, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 63.134, "width_percent": 8.062}, {"sql": "update `ec_order_product` set `tax_amount` = 32.1, `product_type` = 'digital', `ec_order_product`.`updated_at` = '2025-08-14 23:14:05' where `id` = 47", "type": "query", "params": [], "bindings": [32.1, {"value": "digital", "label": "Digital"}, "2025-08-14 23:14:05", 47], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 964}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1273}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 140}], "start": **********.302186, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:964", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=964", "ajax": false, "filename": "OrderHelper.php", "line": "964"}, "connection": "martfury", "explain": null, "start_percent": 71.196, "width_percent": 8.577}, {"sql": "select * from `ec_orders` where `id` = 46 limit 1", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleCheckoutOrderData.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleCheckoutOrderData.php", "line": 60}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 144}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.335291, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 79.773, "width_percent": 8.124}, {"sql": "select * from `ec_customer_addresses` where `ec_customer_addresses`.`customer_id` = 2 and `ec_customer_addresses`.`customer_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 141}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 174}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.343374, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.897, "width_percent": 0.948}, {"sql": "select * from `countries` where `countries`.`id` in (0, 0)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 141}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 174}], "start": **********.344913, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 88.845, "width_percent": 0.619}, {"sql": "select * from `states` where `states`.`id` in (0, 0)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 141}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 174}], "start": **********.346576, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 89.464, "width_percent": 0.619}, {"sql": "select * from `cities` where `cities`.`id` in (0, 0)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 141}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 174}], "start": **********.3487298, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 90.082, "width_percent": 0.825}, {"sql": "select * from `ec_discounts` where `type` = 'coupon' and `display_at_checkout` = 1 and `start_date` <= '2025-08-14 23:14:05' and (`end_date` is null or `end_date` >= '2025-08-14 23:14:05') and (`quantity` is null or `quantity` > `total_used`) and (`store_id` is null or `store_id` in (1))", "type": "query", "params": [], "bindings": ["coupon", 1, "2025-08-14 23:14:05", "2025-08-14 23:14:05", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 201}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.351635, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 90.907, "width_percent": 1.856}, {"sql": "select avg(`star`) as aggregate from `ec_products` inner join `ec_reviews` on `ec_products`.`id` = `ec_reviews`.`product_id` where `ec_products`.`store_id` = 1 and `ec_products`.`store_id` is not null and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "plugins/marketplace::orders.checkout.products", "file": "D:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.389563, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "marketplace::orders.checkout.products:43", "source": {"index": 19, "namespace": "view", "name": "plugins/marketplace::orders.checkout.products", "file": "D:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproducts.blade.php&line=43", "ajax": false, "filename": "products.blade.php", "line": "43"}, "connection": "martfury", "explain": null, "start_percent": 92.763, "width_percent": 1.113}, {"sql": "select count(*) as aggregate from `ec_products` inner join `ec_reviews` on `ec_products`.`id` = `ec_reviews`.`product_id` where `ec_products`.`store_id` = 1 and `ec_products`.`store_id` is not null and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "plugins/marketplace::orders.checkout.products", "file": "D:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.php", "line": 45}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.393414, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "marketplace::orders.checkout.products:45", "source": {"index": 19, "namespace": "view", "name": "plugins/marketplace::orders.checkout.products", "file": "D:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproducts.blade.php&line=45", "ajax": false, "filename": "products.blade.php", "line": "45"}, "connection": "martfury", "explain": null, "start_percent": 93.876, "width_percent": 0.784}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 24 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [24, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1003}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.4241688, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 94.66, "width_percent": 1.258}, {"sql": "select * from `mp_stores` where `id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1299}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1281}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}], "start": **********.5631561, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 95.918, "width_percent": 0.99}, {"sql": "select * from `mp_stores` where `id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1299}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1323}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/botble/platform/js-validation/src/JsValidatorFactory.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\js-validation\\src\\JsValidatorFactory.php", "line": 98}], "start": **********.567726, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 96.907, "width_percent": 0.742}, {"sql": "select * from `mp_stores` where `id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1299}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1281}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}], "start": **********.681739, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 97.649, "width_percent": 1.113}, {"sql": "select * from `mp_stores` where `id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1299}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1323}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/botble/platform/js-validation/src/JsValidatorFactory.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\js-validation\\src\\JsValidatorFactory.php", "line": 98}], "start": **********.686704, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 98.763, "width_percent": 1.237}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Product": {"retrieved": 22, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"retrieved": 15, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Order": {"retrieved": 2, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Address": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FAddress.php&line=1", "ajax": false, "filename": "Address.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderProduct": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderProduct.php&line=1", "ajax": false, "filename": "OrderProduct.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Tax": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}}, "count": 62, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 60, "updated": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/checkout/50da09dec9a537b0bbea6825c33a42fc", "action_name": "public.checkout.information", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController@getCheckout", "uri": "GET checkout/{token}", "controller": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController@getCheckout<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicCheckoutController.php&line=64\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers\\Fronts", "prefix": "/checkout/{token}", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicCheckoutController.php&line=64\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php:64-227</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "duration": "1.4s", "peak_memory": "60MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1751533011 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1751533011\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-914627907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-914627907\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1280832505 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"88 characters\">https://martfury.gc/checkout/50da09dec9a537b0bbea6825c33a42fc?error=1&amp;error_type=payment</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3267 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; xxx111otrckid=b667792b-b878-4cd5-a326-3ac82d49d6c8; XSRF-TOKEN=eyJpdiI6IlJaNHQ4Y2NBQXdsVHQwM08wditaTFE9PSIsInZhbHVlIjoiYnRmTU05dysrdUU1UFBaSGc2RGtZTnd2ZUJjWHFYaURIWTRoeHhjWlE2aVQ3ZTNUQnVrZzAwVnJKU2VFR2JkbWNXanFneWNEcURnS0pSUEgxeGNSWkw2Q0FjcUR3cHYyb0tSVXp3WTVZRTNldHhTRTJYKzRUUFpzUU1IOWdLTksiLCJtYWMiOiJjNWJlNDQ0NDRkMWI3ZGYxMDJjNzQyZmE5NTM5NzllMzdmZjMxMWE0Y2Y5OGE2MGM5NDlkYjhjMmI3ZjUxYWQ0IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InkvbkEwRkYzb1JmbXdkejEybENIU2c9PSIsInZhbHVlIjoibmRXNXhQdXpEWHZraUdjUUJncG5iVDdpamg5WCt3eU5jTjJhNktkckJydTNhTWNTTk8vM1FkenNSZzNXUzF2TmlHWkp0T1hhWUt4Tk1lUlNHVDRlbUowSCtKNVdocDNEWjVERUo1WmJGbFdXTlFrU2JYM0pjcjhHMitMUmFhdTciLCJtYWMiOiI4ZTIwZGI1ZTQ3Mzk3NDk3NTc5MmNhNWNlODZlZjBkOTVjYTUxMWNmMGIwOGUzNGI3NTNhMWI1YjMyOTJjYWYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280832505\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-584366958 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>xxx111otrckid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dhXq3H0q2A1IwAt71xnTkmOnVhkoiBuxG1NoJ54S</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6o7K5Vt18ObXQZVJQrMB8hUNn5eNuW4gUpBf0vtK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-584366958\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1502508090 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 23:14:05 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502508090\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-920715790 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dhXq3H0q2A1IwAt71xnTkmOnVhkoiBuxG1NoJ54S</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"88 characters\">https://martfury.gc/checkout/50da09dec9a537b0bbea6825c33a42fc?error=1&amp;error_type=payment</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">error_msg</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">error_msg</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>7</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>-</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref href=#sf-dump-920715790-ref24783 title=\"3 occurrences\">#4783</a><samp data-depth=3 id=sf-dump-920715790-ref24783 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012af0000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 14, 2025\n- 00:00:00.542497 from now\nDST Off\">2025-08-14 23:14:05.192163 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4388</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>457dbff0be834a20b3903dcc369c5f6b</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4738</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">457dbff0be834a20b3903dcc369c5f6b</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>24</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>4</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"26 characters\">Dual Camera 20MP (Digital)</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>80.25</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4755</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products/1.jpg</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"25 characters\">(Color: Black, Size: XXL)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>VAT</span>\" => <span class=sf-dump-num>10.0</span>\n              </samp>]\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-118-A0</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>848.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>10.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref>#4813</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012cd0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 14, 2025\n- 00:00:00.543047 from now\nDST Off\">2025-08-14 23:14:05.192129 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref>#4798</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012be0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 14, 2025\n- 00:00:00.543122 from now\nDST Off\">2025-08-14 23:14:05.192122 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">50da09dec9a537b0bbea6825c33a42fc</span>\"\n  \"<span class=sf-dump-key>c229db37147d462b82e0445cc4120575</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:24</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref href=#sf-dump-920715790-ref24783 title=\"3 occurrences\">#4783</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>46</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref href=#sf-dump-920715790-ref24783 title=\"3 occurrences\">#4783</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping</span>\" => []\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>shipping_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">hyperpay</span>\"\n  \"<span class=sf-dump-key>error_msg</span>\" => \"<span class=sf-dump-str title=\"68 characters\">Payment failed! Something wrong with your payment. Please try again.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K2NDS9W9FN4Z7X94YZXA02CP</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920715790\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/checkout/50da09dec9a537b0bbea6825c33a42fc", "action_name": "public.checkout.information", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController@getCheckout"}, "badge": null}}