<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e(trans('plugins/hyperpay::hyperpay.checkout_title')); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .checkout-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 30px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        .payment-widget {
            margin: 20px 0;
        }
        .loading-spinner {
            text-align: center;
            padding: 40px;
        }
        .error-message {
            color: #dc3545;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="checkout-container">
            <div class="text-center mb-4">
                <h2><?php echo e(trans('plugins/hyperpay::hyperpay.secure_payment')); ?></h2>
                <p class="text-muted"><?php echo e(trans('plugins/hyperpay::hyperpay.secure_payment_description')); ?></p>
            </div>

            <?php if(isset($checkoutId) && isset($scriptUrl)): ?>
                <div class="payment-widget">
                    <script src="<?php echo e($scriptUrl); ?>"></script>
                    <form
                        action="<?php echo e(route('payments.hyperpay.callback', ['checkout_id' => $checkoutId, 'order_id' => $orderId ?? ''])); ?>"
                        class="paymentWidgets"
                        data-brands="<?php echo e($paymentBrands ?? 'VISA MASTER'); ?>"
                    ></form>
                </div>

                <div class="text-center mt-4">
                    <small class="text-muted">
                        <?php echo e(trans('plugins/hyperpay::hyperpay.powered_by')); ?>

                        <img src="<?php echo e(url('vendor/core/plugins/hyperpay/images/hyperpay-logo.png')); ?>"
                             alt="HyperPay"
                             style="height: 20px; margin-left: 5px;">
                    </small>
                </div>
            <?php else: ?>
                <div class="error-message text-center">
                    <h4><?php echo e(trans('plugins/hyperpay::hyperpay.checkout_error')); ?></h4>
                    <p><?php echo e(trans('plugins/hyperpay::hyperpay.checkout_error_description')); ?></p>
                    <a href="<?php echo e(url()->previous()); ?>" class="btn btn-primary">
                        <?php echo e(trans('plugins/hyperpay::hyperpay.go_back')); ?>

                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Handle payment form submission
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.paymentWidgets');
            if (form) {
                form.addEventListener('submit', function(e) {
                    // Show loading state
                    const submitButton = form.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.innerHTML = '<?php echo e(trans('plugins/hyperpay::hyperpay.processing')); ?>...';
                    }
                });
            }
        });
    </script>
</body>
</html>
<?php /**PATH D:\laragon\www\martfury\platform/plugins/hyperpay/resources/views/checkout.blade.php ENDPATH**/ ?>