<?php if(setting('payment_cmi_status') == 1): ?>
    <li class="list-group-item">
        <input
            class="magic-radio js_payment_method"
            id="payment_cmi"
            name="payment_method"
            type="radio"
            value="cmi"
            <?php if($selecting == CMI_PAYMENT_METHOD_NAME): ?> checked <?php endif; ?>
        >
        <label class="text-start" for="payment_cmi">
            <?php echo e(get_payment_setting('name', 'cmi', trans('plugins/payment::payment.payment_via_cmi'))); ?>

        </label>

        <div
            class="payment_cmi_wrap payment_collapse_wrap collapse <?php if($selecting == CMI_PAYMENT_METHOD_NAME): ?> show <?php endif; ?>"
            style="padding: 15px 0;"
        >
            <p><?php echo BaseHelper::clean(get_payment_setting('description', 'cmi')); ?></p>

            

        </div>
    </li>
<?php endif; ?>
<?php /**PATH D:\laragon\www\martfury\platform/plugins/cmi/resources/views/methods.blade.php ENDPATH**/ ?>