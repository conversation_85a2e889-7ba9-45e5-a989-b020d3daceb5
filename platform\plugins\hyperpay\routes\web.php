<?php

use Bo<PERSON><PERSON>\HyperPay\Http\Controllers\HyperPayController;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Botble\HyperPay\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(['prefix' => 'payment/hyperpay'], function () {
        Route::get('checkout/{checkoutId}', [HyperPayController::class, 'checkout'])
            ->name('payments.hyperpay.checkout');
            
        Route::get('callback', [HyperPayController::class, 'callback'])
            ->name('payments.hyperpay.callback');
            
        Route::post('webhook', [HyperPayController::class, 'webhook'])
            ->name('payments.hyperpay.webhook');
            
        Route::get('status/{checkoutId}', [HyperPayController::class, 'status'])
            ->name('payments.hyperpay.status');
    });
});
