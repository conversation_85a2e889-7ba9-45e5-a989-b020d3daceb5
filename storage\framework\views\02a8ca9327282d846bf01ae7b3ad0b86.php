<?php if(setting('payment_hyperpay_status') == 1): ?>
    <?php
        $hyperPayService = new \Botble\HyperPay\Services\Gateways\HyperPayPaymentService();
        $availablePaymentTypes = $hyperPayService->getAvailablePaymentTypes();
    ?>

    <?php if (isset($component)) { $__componentOriginal69057bacd9705b1c669802ff37556f6e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69057bacd9705b1c669802ff37556f6e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'fa081de1c3ee47622336b4eeafa01705::payment-method','data' => ['name' => HYPERPAY_PAYMENT_METHOD_NAME,'paymentName' => 'HyperPay','supportedCurrencies' => $hyperPayService->supportedCurrencyCodes()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('plugins-payment::payment-method'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(HYPERPAY_PAYMENT_METHOD_NAME),'paymentName' => 'HyperPay','supportedCurrencies' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hyperPayService->supportedCurrencyCodes())]); ?>
        <div class="hyperpay-payment-form" style="max-width: 500px">
            <?php if(count($availablePaymentTypes) > 1): ?>
                <div class="form-group mb-3">
                    <label class="form-label"><?php echo e(trans('plugins/hyperpay::hyperpay.select_payment_type')); ?></label>
                    <div class="payment-type-selection">
                        <?php $__currentLoopData = $availablePaymentTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="form-check">
                                <input 
                                    class="form-check-input" 
                                    type="radio" 
                                    name="hyperpay_payment_type" 
                                    id="hyperpay_<?php echo e($type); ?>" 
                                    value="<?php echo e($type); ?>"
                                    <?php echo e($loop->first ? 'checked' : ''); ?>

                                >
                                <label class="form-check-label" for="hyperpay_<?php echo e($type); ?>">
                                    <img src="<?php echo e(url('vendor/core/plugins/hyperpay/images/' . $type . '.png')); ?>" 
                                         alt="<?php echo e($label); ?>" 
                                         style="height: 24px; margin-right: 8px;">
                                    <?php echo e($label); ?>

                                </label>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php else: ?>
                <?php $__currentLoopData = $availablePaymentTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <input type="hidden" name="hyperpay_payment_type" value="<?php echo e($type); ?>">
                    <div class="payment-type-display mb-3">
                        <img src="<?php echo e(url('vendor/core/plugins/hyperpay/images/' . $type . '.png')); ?>" 
                             alt="<?php echo e($label); ?>" 
                             style="height: 32px; margin-right: 8px;">
                        <span><?php echo e($label); ?></span>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            <div class="hyperpay-checkout-container" id="hyperpay-checkout-container" style="display: none;">
                <!-- HyperPay widget will be loaded here -->
            </div>

            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle"></i>
                <?php echo e(trans('plugins/hyperpay::hyperpay.payment_info')); ?>

            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const paymentTypeInputs = document.querySelectorAll('input[name="hyperpay_payment_type"]');
                
                paymentTypeInputs.forEach(function(input) {
                    input.addEventListener('change', function() {
                        // Update the selected payment type
                        console.log('Selected payment type:', this.value);
                    });
                });
            });
        </script>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69057bacd9705b1c669802ff37556f6e)): ?>
<?php $attributes = $__attributesOriginal69057bacd9705b1c669802ff37556f6e; ?>
<?php unset($__attributesOriginal69057bacd9705b1c669802ff37556f6e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69057bacd9705b1c669802ff37556f6e)): ?>
<?php $component = $__componentOriginal69057bacd9705b1c669802ff37556f6e; ?>
<?php unset($__componentOriginal69057bacd9705b1c669802ff37556f6e); ?>
<?php endif; ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\martfury\platform/plugins/hyperpay/resources/views/methods.blade.php ENDPATH**/ ?>