<?php

namespace Bo<PERSON><PERSON>\HyperPay\Providers;

use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Payment\Enums\PaymentMethodEnum;
use Bo<PERSON>ble\Payment\Facades\PaymentMethods;
use Bo<PERSON><PERSON>\Payment\Supports\PaymentFeeHelper;
use Bo<PERSON>ble\HyperPay\Forms\HyperPayPaymentMethodForm;
use Bo<PERSON>ble\HyperPay\Services\Gateways\HyperPayPaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerHyperPayMethod'], 1, 2);

        $this->app->booted(function (): void {
            add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithHyperPay'], 1, 2);
        });

        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 1);

        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == PaymentMethodEnum::class) {
                $values['HYPERPAY'] = HYPERPAY_PAYMENT_METHOD_NAME;
            }

            return $values;
        }, 1, 2);

        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == HYPERPAY_PAYMENT_METHOD_NAME) {
                $value = 'HyperPay';
            }

            return $value;
        }, 1, 2);

        add_filter(BASE_FILTER_ENUM_HTML, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == HYPERPAY_PAYMENT_METHOD_NAME) {
                $value = Html::tag(
                    'span',
                    PaymentMethodEnum::getLabel($value),
                    ['class' => 'label-success status-label']
                )
                    ->toHtml();
            }

            return $value;
        }, 1, 2);

        add_filter(PAYMENT_FILTER_GET_SERVICE_CLASS, function ($data, $value) {
            if ($value == HYPERPAY_PAYMENT_METHOD_NAME) {
                $data = HyperPayPaymentService::class;
            }

            return $data;
        }, 1, 2);

        add_filter(PAYMENT_FILTER_PAYMENT_INFO_DETAIL, function ($data, $payment) {
            if ($payment->payment_channel == HYPERPAY_PAYMENT_METHOD_NAME) {
                $paymentDetail = (new HyperPayPaymentService())->getPaymentDetails($payment->charge_id);

                if ($paymentDetail) {
                    $data = view('plugins/hyperpay::detail', ['payment' => $paymentDetail])->render();
                }
            }

            return $data;
        }, 1, 2);
    }

    public function addPaymentSettings(?string $settings): string
    {
        return $settings . HyperPayPaymentMethodForm::create()->renderForm();
    }

    public function registerHyperPayMethod(?string $html, array $data): string
    {
        PaymentMethods::method(HYPERPAY_PAYMENT_METHOD_NAME, [
            'html' => view('plugins/hyperpay::methods', $data)->render(),
        ]);

        return $html;
    }

    public function checkoutWithHyperPay(array $data, Request $request): array
    {
        if ($data['type'] !== HYPERPAY_PAYMENT_METHOD_NAME) {
            return $data;
        }

        $hyperPayService = $this->app->make(HyperPayPaymentService::class);

        $currentCurrency = get_application_currency();
        $paymentData = apply_filters(PAYMENT_FILTER_PAYMENT_DATA, [], $request);

        $orderAmount = $paymentData['amount'] ?? 0;
        $paymentFee = 0;
        if (is_plugin_active('payment')) {
            $paymentFee = PaymentFeeHelper::calculateFee(HYPERPAY_PAYMENT_METHOD_NAME, $orderAmount);
        }

        $paymentData['payment_fee'] = $paymentFee;

        if (!isset($paymentData['currency'])) {
            $paymentData['currency'] = strtoupper(get_application_currency()->title);
        }

        $supportedCurrencies = $hyperPayService->supportedCurrencyCodes();

        // Currency conversion logic
        if (!in_array($paymentData['currency'], $supportedCurrencies) && strtoupper($currentCurrency->title) !== 'SAR') {
            $currencyModel = $currentCurrency->replicate();
            $supportedCurrency = $currencyModel->query()->where('title', 'SAR')->first();

            if ($supportedCurrency) {
                $paymentData['currency'] = strtoupper($supportedCurrency->title);
                if ($currentCurrency->is_default) {
                    $paymentData['amount'] = $paymentData['amount'] * $supportedCurrency->exchange_rate;
                } else {
                    $paymentData['amount'] = format_price(
                        $paymentData['amount'] / $currentCurrency->exchange_rate,
                        $currentCurrency,
                        true
                    );
                }
            }
        }

        if (!in_array($paymentData['currency'], $supportedCurrencies)) {
            $data['error'] = true;
            $data['message'] = __(
                ":name doesn't support :currency. List of currencies supported by :name: :currencies.",
                [
                    'name' => 'HyperPay',
                    'currency' => $paymentData['currency'],
                    'currencies' => implode(', ', $supportedCurrencies),
                ]
            );

            return $data;
        }

        // Check if HyperPay is properly configured
        if (!$hyperPayService->isAvailable()) {
            $data['error'] = true;
            $data['message'] = __('HyperPay is not properly configured. Please check your settings.');
            return $data;
        }

        // Get payment type from request (default to visa)
        $paymentType = $request->input('hyperpay_payment_type', 'visa');
        $paymentData['payment_type'] = $paymentType;

        $result = $hyperPayService->execute($paymentData);

        if ($hyperPayService->getErrorMessage()) {
            $data['error'] = true;
            $data['message'] = $hyperPayService->getErrorMessage();
        } elseif ($result) {
            // Store checkout data in session for the payment form
            session([
                'hyperpay_checkout_id' => $result,
                'hyperpay_payment_type' => $paymentType,
                'hyperpay_order_id' => $paymentData['order_id'] ?? null,
                'hyperpay_amount' => $paymentData['amount'] ?? 0,
                'hyperpay_currency' => $paymentData['currency'] ?? 'SAR',
            ]);

            // Return checkout URL for redirect to payment form
            $data['checkoutUrl'] = route('payments.hyperpay.checkout', [
                'checkout_id' => $result,
                'payment_type' => $paymentType
            ]);
        } else {
            $data['error'] = true;
            $data['message'] = __('Failed to create HyperPay checkout');
        }

        return $data;
    }
}
