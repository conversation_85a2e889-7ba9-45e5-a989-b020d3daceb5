{"__meta": {"id": "01K2NFN4Q56QYK3G4JK09F7E71", "datetime": "2025-08-14 23:46:45", "utime": **********.094652, "method": "PUT", "uri": "/admin/plugins/status?name=erpnext", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": 1755215202.434799, "end": **********.094669, "duration": 2.659870147705078, "duration_str": "2.66s", "measures": [{"label": "Booting", "start": 1755215202.434799, "relative_start": 0, "end": **********.246269, "relative_end": **********.246269, "duration": 0.****************, "duration_str": "811ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.24629, "relative_start": 0.****************, "end": **********.094671, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.85s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.26759, "relative_start": 0.****************, "end": **********.274279, "relative_end": **********.274279, "duration": 0.0066890716552734375, "duration_str": "6.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.089868, "relative_start": 2.**************, "end": **********.091684, "relative_end": **********.091684, "duration": 0.0018160343170166016, "duration_str": "1.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 218, "nb_statements": 218, "nb_visible_statements": 218, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.3018900000000004, "accumulated_duration_str": "1.3s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 118 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.289118, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 0.03}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.293654, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "martfury", "explain": null, "start_percent": 0.03, "width_percent": 0.031}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.3000891, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.061, "width_percent": 0.039}, {"sql": "select `key` from `settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 17, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 18, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.584908, "duration": 0.01231, "duration_str": "12.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:45", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=45", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "45"}, "connection": "martfury", "explain": null, "start_percent": 0.101, "width_percent": 0.946}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'api_enabled'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "api_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.600116, "duration": 0.0264, "duration_str": "26.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 1.046, "width_percent": 2.028}, {"sql": "update `settings` set `value` = '[\\\"language\\\",\\\"language-advanced\\\",\\\"ads\\\",\\\"analytics\\\",\\\"audit-log\\\",\\\"backup\\\",\\\"blog\\\",\\\"captcha\\\",\\\"contact\\\",\\\"cookie-consent\\\",\\\"ecommerce\\\",\\\"faq\\\",\\\"location\\\",\\\"marketplace\\\",\\\"mollie\\\",\\\"newsletter\\\",\\\"payment\\\",\\\"paypal\\\",\\\"paypal-payout\\\",\\\"paystack\\\",\\\"razorpay\\\",\\\"shippo\\\",\\\"simple-slider\\\",\\\"social-login\\\",\\\"sslcommerz\\\",\\\"stripe\\\",\\\"stripe-connect\\\",\\\"translation\\\",\\\"cmi\\\",\\\"fob-email-log\\\",\\\"google-merchant-feed\\\",\\\"paymob\\\",\\\"quote\\\",\\\"tabby\\\",\\\"hyperpay\\\"]', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'activated_plugins'", "type": "query", "params": [], "bindings": ["[\"language\",\"language-advanced\",\"ads\",\"analytics\",\"audit-log\",\"backup\",\"blog\",\"captcha\",\"contact\",\"cookie-consent\",\"ecommerce\",\"faq\",\"location\",\"marketplace\",\"mollie\",\"newsletter\",\"payment\",\"paypal\",\"paypal-payout\",\"paystack\",\"razorpay\",\"shippo\",\"simple-slider\",\"social-login\",\"sslcommerz\",\"stripe\",\"stripe-connect\",\"translation\",\"cmi\",\"fob-email-log\",\"google-merchant-feed\",\"paymob\",\"quote\",\"tabby\",\"hyperpay\"]", "2025-08-14 23:46:43", "activated_plugins"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.627995, "duration": 0.00738, "duration_str": "7.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 3.074, "width_percent": 0.567}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'analytics_dashboard_widgets'", "type": "query", "params": [], "bindings": ["0", "2025-08-14 23:46:43", "analytics_dashboard_widgets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.636611, "duration": 0.013380000000000001, "duration_str": "13.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 3.641, "width_percent": 1.028}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'enable_recaptcha_botble_contact_forms_fronts_contact_form'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "enable_recaptcha_botble_contact_forms_fronts_contact_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.6515532, "duration": 0.016280000000000003, "duration_str": "16.28ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 4.669, "width_percent": 1.25}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'api_layer_api_key'", "type": "query", "params": [], "bindings": ["", "2025-08-14 23:46:43", "api_layer_api_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.6692362, "duration": 0.02978, "duration_str": "29.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 5.919, "width_percent": 2.287}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'enable_recaptcha_botble_newsletter_forms_fronts_newsletter_form'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "enable_recaptcha_botble_newsletter_forms_fronts_newsletter_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.701561, "duration": 0.0049299999999999995, "duration_str": "4.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 8.207, "width_percent": 0.379}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'language_hide_default'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "language_hide_default"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.708018, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 8.585, "width_percent": 0.363}, {"sql": "update `settings` set `value` = 'dropdown', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'language_switcher_display'", "type": "query", "params": [], "bindings": ["dropdown", "2025-08-14 23:46:43", "language_switcher_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.7151659, "duration": 0.012, "duration_str": "12ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 8.949, "width_percent": 0.922}, {"sql": "update `settings` set `value` = 'all', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'language_display'", "type": "query", "params": [], "bindings": ["all", "2025-08-14 23:46:43", "language_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.7285209, "duration": 0.00495, "duration_str": "4.95ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 9.87, "width_percent": 0.38}, {"sql": "update `settings` set `value` = '[]', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'language_hide_languages'", "type": "query", "params": [], "bindings": ["[]", "2025-08-14 23:46:43", "language_hide_languages"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.7350051, "duration": 0.01453, "duration_str": "14.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 10.25, "width_percent": 1.116}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'simple_slider_using_assets'", "type": "query", "params": [], "bindings": ["0", "2025-08-14 23:46:43", "simple_slider_using_assets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.7512028, "duration": 0.02903, "duration_str": "29.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 11.367, "width_percent": 2.23}, {"sql": "update `settings` set `value` = '05f40f708f32be2da075d57c4f3beb37', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'media_random_hash'", "type": "query", "params": [], "bindings": ["05f40f708f32be2da075d57c4f3beb37", "2025-08-14 23:46:43", "media_random_hash"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.7815528, "duration": 0.015710000000000002, "duration_str": "15.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 13.596, "width_percent": 1.207}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'payment_cod_status'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "payment_cod_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.799597, "duration": 0.011269999999999999, "duration_str": "11.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 14.803, "width_percent": 0.866}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'payment_bank_transfer_status'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "payment_bank_transfer_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.812722, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 15.669, "width_percent": 0.349}, {"sql": "update `settings` set `value` = 'martfury', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'theme'", "type": "query", "params": [], "bindings": ["martfury", "2025-08-14 23:46:43", "theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.818634, "duration": 0.004849999999999999, "duration_str": "4.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 16.017, "width_percent": 0.373}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'show_admin_bar'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "show_admin_bar"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.824664, "duration": 0.0114, "duration_str": "11.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 16.39, "width_percent": 0.876}, {"sql": "update `settings` set `value` = 'general/favicon.png', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'admin_favicon'", "type": "query", "params": [], "bindings": ["general/favicon.png", "2025-08-14 23:46:43", "admin_favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.837458, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 17.266, "width_percent": 0.311}, {"sql": "update `settings` set `value` = 'general/logo-light.png', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'admin_logo'", "type": "query", "params": [], "bindings": ["general/logo-light.png", "2025-08-14 23:46:43", "admin_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.842807, "duration": 0.00666, "duration_str": "6.66ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 17.577, "width_percent": 0.512}, {"sql": "update `settings` set `value` = 'blog', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'permalink-botble-blog-models-post'", "type": "query", "params": [], "bindings": ["blog", "2025-08-14 23:46:43", "permalink-botble-blog-models-post"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.85068, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 18.088, "width_percent": 0.333}, {"sql": "update `settings` set `value` = 'blog', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'permalink-botble-blog-models-category'", "type": "query", "params": [], "bindings": ["blog", "2025-08-14 23:46:43", "permalink-botble-blog-models-category"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.856221, "duration": 0.009, "duration_str": "9ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 18.421, "width_percent": 0.691}, {"sql": "update `settings` set `value` = 'Please pay money directly to the postman, if you choose cash on delivery method (COD).', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'payment_cod_description'", "type": "query", "params": [], "bindings": ["Please pay money directly to the postman, if you choose cash on delivery method (COD).", "2025-08-14 23:46:43", "payment_cod_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.866441, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 19.112, "width_percent": 0.331}, {"sql": "update `settings` set `value` = 'Please send money to our bank account: ACB - 69270 213 19.', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'payment_bank_transfer_description'", "type": "query", "params": [], "bindings": ["Please send money to our bank account: ACB - 69270 213 19.", "2025-08-14 23:46:43", "payment_bank_transfer_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.871946, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 19.443, "width_percent": 0.336}, {"sql": "update `settings` set `value` = 'stripe_checkout', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'payment_stripe_payment_type'", "type": "query", "params": [], "bindings": ["stripe_checkout", "2025-08-14 23:46:43", "payment_stripe_payment_type"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.878308, "duration": 0.00474, "duration_str": "4.74ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 19.78, "width_percent": 0.364}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'plugins_ecommerce_customer_new_order_status'", "type": "query", "params": [], "bindings": ["0", "2025-08-14 23:46:43", "plugins_ecommerce_customer_new_order_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.884301, "duration": 0.02022, "duration_str": "20.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 20.144, "width_percent": 1.553}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'plugins_ecommerce_admin_new_order_status'", "type": "query", "params": [], "bindings": ["0", "2025-08-14 23:46:43", "plugins_ecommerce_admin_new_order_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.9058988, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 21.697, "width_percent": 0.302}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_is_enabled_support_digital_products'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "ecommerce_is_enabled_support_digital_products"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.9111311, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 21.999, "width_percent": 0.335}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_load_countries_states_cities_from_location_plugin'", "type": "query", "params": [], "bindings": ["0", "2025-08-14 23:46:43", "ecommerce_load_countries_states_cities_from_location_plugin"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.916717, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 22.334, "width_percent": 0.306}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'payment_bank_transfer_display_bank_info_at_the_checkout_success_page'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "payment_bank_transfer_display_bank_info_at_the_checkout_success_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.921849, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 22.639, "width_percent": 0.301}, {"sql": "update `settings` set `value` = 'MF-2443-[%S]', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_product_sku_format'", "type": "query", "params": [], "bindings": ["MF-2443-[%S]", "2025-08-14 23:46:43", "ecommerce_product_sku_format"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.926914, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 22.94, "width_percent": 0.327}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_enable_product_specification'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:43", "ecommerce_enable_product_specification"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.932317, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 23.268, "width_percent": 0.307}, {"sql": "update `settings` set `value` = 'Martfury', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_store_name'", "type": "query", "params": [], "bindings": ["Martfury", "2025-08-14 23:46:43", "ecommerce_store_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.937602, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 23.575, "width_percent": 0.312}, {"sql": "update `settings` set `value` = '1800979769', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_store_phone'", "type": "query", "params": [], "bindings": ["1800979769", "2025-08-14 23:46:43", "ecommerce_store_phone"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.942867, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 23.887, "width_percent": 0.32}, {"sql": "update `settings` set `value` = '502 New Street', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_store_address'", "type": "query", "params": [], "bindings": ["502 New Street", "2025-08-14 23:46:43", "ecommerce_store_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.948816, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 24.206, "width_percent": 0.347}, {"sql": "update `settings` set `value` = 'Brighton VIC', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_store_state'", "type": "query", "params": [], "bindings": ["Brighton VIC", "2025-08-14 23:46:43", "ecommerce_store_state"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.954633, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 24.554, "width_percent": 0.305}, {"sql": "update `settings` set `value` = 'Brighton VIC', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_store_city'", "type": "query", "params": [], "bindings": ["Brighton VIC", "2025-08-14 23:46:43", "ecommerce_store_city"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.959766, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 24.858, "width_percent": 0.313}, {"sql": "update `settings` set `value` = 'AU', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'ecommerce_store_country'", "type": "query", "params": [], "bindings": ["AU", "2025-08-14 23:46:43", "ecommerce_store_country"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.965124, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 25.171, "width_percent": 0.312}, {"sql": "update `settings` set `value` = 'MartFury - Laravel Ecommerce system', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'theme-martfury-site_title'", "type": "query", "params": [], "bindings": ["MartFury - Laravel Ecommerce system", "2025-08-14 23:46:43", "theme-martfury-site_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.970509, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 25.483, "width_percent": 0.3}, {"sql": "update `settings` set `value` = 'MartFury is a clean & modern Laravel Ecommerce System for multipurpose online stores. With design clean and trendy, MartFury will make your online store look more impressive and attractive to viewers.', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'theme-martfury-seo_description'", "type": "query", "params": [], "bindings": ["MartFury is a clean & modern Laravel Ecommerce System for multipurpose online stores. With design clean and trendy, MartFury will make your online store look more impressive and attractive to viewers.", "2025-08-14 23:46:43", "theme-martfury-seo_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.975579, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 25.783, "width_percent": 0.309}, {"sql": "update `settings` set `value` = '© %Y MartFury. All Rights Reserved.', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'theme-martfury-copyright'", "type": "query", "params": [], "bindings": ["© %Y MartFury. All Rights Reserved.", "2025-08-14 23:46:43", "theme-martfury-copyright"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.9808898, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 26.092, "width_percent": 0.315}, {"sql": "update `settings` set `value` = 'general/favicon.png', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'theme-martfury-favicon'", "type": "query", "params": [], "bindings": ["general/favicon.png", "2025-08-14 23:46:43", "theme-martfury-favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.9862, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 26.407, "width_percent": 0.32}, {"sql": "update `settings` set `value` = 'general/logo.png', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'theme-martfury-logo'", "type": "query", "params": [], "bindings": ["general/logo.png", "2025-08-14 23:46:43", "theme-martfury-logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.991594, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 26.727, "width_percent": 0.324}, {"sql": "update `settings` set `value` = 'Welcome to MartFury Online Shopping Store!', `settings`.`updated_at` = '2025-08-14 23:46:43' where `key` = 'theme-martfury-welcome_message'", "type": "query", "params": [], "bindings": ["Welcome to MartFury Online Shopping Store!", "2025-08-14 23:46:43", "theme-martfury-welcome_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.9971051, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 27.051, "width_percent": 0.32}, {"sql": "update `settings` set `value` = '502 New Street, Brighton VIC, Australia', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-address'", "type": "query", "params": [], "bindings": ["502 New Street, Brighton VIC, Australia", "2025-08-14 23:46:44", "theme-martfury-address"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.002508, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 27.371, "width_percent": 0.315}, {"sql": "update `settings` set `value` = '1800 97 97 69', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-hotline'", "type": "query", "params": [], "bindings": ["1800 97 97 69", "2025-08-14 23:46:44", "theme-martfury-hotline"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.007785, "duration": 0.01329, "duration_str": "13.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 27.686, "width_percent": 1.021}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-email'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-14 23:46:44", "theme-martfury-email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.022351, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 28.707, "width_percent": 0.337}, {"sql": "update `settings` set `value` = 'general/newsletter.jpg', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-newsletter_image'", "type": "query", "params": [], "bindings": ["general/newsletter.jpg", "2025-08-14 23:46:44", "theme-martfury-newsletter_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.028715, "duration": 0.0048, "duration_str": "4.8ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 29.044, "width_percent": 0.369}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-homepage_id'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:44", "theme-martfury-homepage_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.0348532, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 29.413, "width_percent": 0.317}, {"sql": "update `settings` set `value` = '6', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-blog_page_id'", "type": "query", "params": [], "bindings": ["6", "2025-08-14 23:46:44", "theme-martfury-blog_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.040376, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 29.73, "width_percent": 0.312}, {"sql": "update `settings` set `value` = 'Your experience on this site will be improved by allowing cookies ', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-cookie_consent_message'", "type": "query", "params": [], "bindings": ["Your experience on this site will be improved by allowing cookies ", "2025-08-14 23:46:44", "theme-martfury-cookie_consent_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.0459151, "duration": 0.00455, "duration_str": "4.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 30.042, "width_percent": 0.349}, {"sql": "update `settings` set `value` = '/cookie-policy', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-cookie_consent_learn_more_url'", "type": "query", "params": [], "bindings": ["/cookie-policy", "2025-08-14 23:46:44", "theme-martfury-cookie_consent_learn_more_url"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.051692, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 30.391, "width_percent": 0.344}, {"sql": "update `settings` set `value` = 'Cookie Policy', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-cookie_consent_learn_more_text'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "2025-08-14 23:46:44", "theme-martfury-cookie_consent_learn_more_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.0573828, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 30.735, "width_percent": 0.315}, {"sql": "update `settings` set `value` = '42', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-number_of_products_per_page'", "type": "query", "params": [], "bindings": ["42", "2025-08-14 23:46:44", "theme-martfury-number_of_products_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.062863, "duration": 0.004690000000000001, "duration_str": "4.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 31.05, "width_percent": 0.36}, {"sql": "update `settings` set `value` = 'Shipping worldwide', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-product_feature_1_title'", "type": "query", "params": [], "bindings": ["Shipping worldwide", "2025-08-14 23:46:44", "theme-martfury-product_feature_1_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.068764, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 31.41, "width_percent": 0.319}, {"sql": "update `settings` set `value` = 'icon-network', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-product_feature_1_icon'", "type": "query", "params": [], "bindings": ["icon-network", "2025-08-14 23:46:44", "theme-martfury-product_feature_1_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.074122, "duration": 0.01459, "duration_str": "14.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 31.729, "width_percent": 1.121}, {"sql": "update `settings` set `value` = 'Free 7-day return if eligible, so easy', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-product_feature_2_title'", "type": "query", "params": [], "bindings": ["Free 7-day return if eligible, so easy", "2025-08-14 23:46:44", "theme-martfury-product_feature_2_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.089925, "duration": 0.01491, "duration_str": "14.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 32.85, "width_percent": 1.145}, {"sql": "update `settings` set `value` = 'icon-3d-rotate', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-product_feature_2_icon'", "type": "query", "params": [], "bindings": ["icon-3d-rotate", "2025-08-14 23:46:44", "theme-martfury-product_feature_2_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.106084, "duration": 0.018359999999999998, "duration_str": "18.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 33.995, "width_percent": 1.41}, {"sql": "update `settings` set `value` = 'Supplier give bills for this product.', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-product_feature_3_title'", "type": "query", "params": [], "bindings": ["Supplier give bills for this product.", "2025-08-14 23:46:44", "theme-martfury-product_feature_3_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.12596, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 35.405, "width_percent": 0.363}, {"sql": "update `settings` set `value` = 'icon-receipt', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-product_feature_3_icon'", "type": "query", "params": [], "bindings": ["icon-receipt", "2025-08-14 23:46:44", "theme-martfury-product_feature_3_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.133096, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 35.769, "width_percent": 0.349}, {"sql": "update `settings` set `value` = 'Pay online or when receiving goods', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-product_feature_4_title'", "type": "query", "params": [], "bindings": ["Pay online or when receiving goods", "2025-08-14 23:46:44", "theme-martfury-product_feature_4_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.138941, "duration": 0.00467, "duration_str": "4.67ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 36.117, "width_percent": 0.359}, {"sql": "update `settings` set `value` = 'icon-credit-card', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-product_feature_4_icon'", "type": "query", "params": [], "bindings": ["icon-credit-card", "2025-08-14 23:46:44", "theme-martfury-product_feature_4_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.145647, "duration": 0.00591, "duration_str": "5.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 36.476, "width_percent": 0.454}, {"sql": "update `settings` set `value` = 'Contact Directly', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_1_title'", "type": "query", "params": [], "bindings": ["Contact Directly", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_1_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.153088, "duration": 0.004849999999999999, "duration_str": "4.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 36.93, "width_percent": 0.373}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_1_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_1_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.1593912, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 37.303, "width_percent": 0.344}, {"sql": "update `settings` set `value` = '(+004) 912-3548-07', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_1_details'", "type": "query", "params": [], "bindings": ["(+004) 912-3548-07", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_1_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.165595, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 37.647, "width_percent": 0.325}, {"sql": "update `settings` set `value` = 'Headquarters', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_2_title'", "type": "query", "params": [], "bindings": ["Headquarters", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_2_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.1713328, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 37.972, "width_percent": 0.315}, {"sql": "update `settings` set `value` = '17 Queen St, South bank, Melbourne 10560, Australia', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_2_subtitle'", "type": "query", "params": [], "bindings": ["17 Queen St, South bank, Melbourne 10560, Australia", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_2_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.17686, "duration": 0.00786, "duration_str": "7.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 38.287, "width_percent": 0.604}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_2_details'", "type": "query", "params": [], "bindings": ["", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_2_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.1860828, "duration": 0.009550000000000001, "duration_str": "9.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 38.89, "width_percent": 0.734}, {"sql": "update `settings` set `value` = 'Work With Us', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_3_title'", "type": "query", "params": [], "bindings": ["Work With Us", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_3_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.197242, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 39.624, "width_percent": 0.338}, {"sql": "update `settings` set `value` = 'Send your CV to our email:', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_3_subtitle'", "type": "query", "params": [], "bindings": ["Send your CV to our email:", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_3_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.2028942, "duration": 0.00694, "duration_str": "6.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 39.962, "width_percent": 0.533}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_3_details'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_3_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.2113059, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 40.495, "width_percent": 0.381}, {"sql": "update `settings` set `value` = 'Customer Service', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_4_title'", "type": "query", "params": [], "bindings": ["Customer Service", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_4_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.217437, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 40.876, "width_percent": 0.323}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_4_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_4_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.222806, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 41.199, "width_percent": 0.33}, {"sql": "update `settings` set `value` = '(800) 843-2446', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_4_details'", "type": "query", "params": [], "bindings": ["(800) 843-2446", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_4_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.228655, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 41.529, "width_percent": 0.392}, {"sql": "update `settings` set `value` = 'Media Relations', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_5_title'", "type": "query", "params": [], "bindings": ["Media Relations", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_5_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.234951, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 41.921, "width_percent": 0.319}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_5_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_5_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.240232, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 42.239, "width_percent": 0.318}, {"sql": "update `settings` set `value` = '(801) 947-3564', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_5_details'", "type": "query", "params": [], "bindings": ["(801) 947-3564", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_5_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.245708, "duration": 0.00498, "duration_str": "4.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 42.557, "width_percent": 0.383}, {"sql": "update `settings` set `value` = 'Vendor Support', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_6_title'", "type": "query", "params": [], "bindings": ["Vendor Support", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_6_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.251898, "duration": 0.00451, "duration_str": "4.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 42.94, "width_percent": 0.346}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_6_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_6_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.25756, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 43.286, "width_percent": 0.344}, {"sql": "update `settings` set `value` = '(801) 947-3100', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-contact_info_box_6_details'", "type": "query", "params": [], "bindings": ["(801) 947-3100", "2025-08-14 23:46:44", "theme-martfury-contact_info_box_6_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.263593, "duration": 0.005, "duration_str": "5ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 43.63, "width_percent": 0.384}, {"sql": "update `settings` set `value` = '7', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-number_of_cross_sale_product'", "type": "query", "params": [], "bindings": ["7", "2025-08-14 23:46:44", "theme-martfury-number_of_cross_sale_product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.2699208, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 44.014, "width_percent": 0.356}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-logo_in_the_checkout_page'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-08-14 23:46:44", "theme-martfury-logo_in_the_checkout_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.275716, "duration": 0.011179999999999999, "duration_str": "11.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 44.371, "width_percent": 0.859}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-logo_in_invoices'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-08-14 23:46:44", "theme-martfury-logo_in_invoices"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.2882051, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 45.23, "width_percent": 0.317}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-logo_vendor_dashboard'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-08-14 23:46:44", "theme-martfury-logo_vendor_dashboard"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.293446, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 45.547, "width_percent": 0.326}, {"sql": "update `settings` set `value` = 'Work Sans', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-primary_font'", "type": "query", "params": [], "bindings": ["Work Sans", "2025-08-14 23:46:44", "theme-martfury-primary_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.2989411, "duration": 0.0045, "duration_str": "4.5ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 45.873, "width_percent": 0.346}, {"sql": "update `settings` set `value` = '[\\\"general\\\\/payment-method-1.jpg\\\",\\\"general\\\\/payment-method-2.jpg\\\",\\\"general\\\\/payment-method-3.jpg\\\",\\\"general\\\\/payment-method-4.jpg\\\",\\\"general\\\\/payment-method-5.jpg\\\"]', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-payment_methods'", "type": "query", "params": [], "bindings": ["[\"general\\/payment-method-1.jpg\",\"general\\/payment-method-2.jpg\",\"general\\/payment-method-3.jpg\",\"general\\/payment-method-4.jpg\",\"general\\/payment-method-5.jpg\"]", "2025-08-14 23:46:44", "theme-martfury-payment_methods"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.304762, "duration": 0.015710000000000002, "duration_str": "15.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 46.219, "width_percent": 1.207}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"Facebook\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-facebook\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/www.facebook.com\\\"}],[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"X (Twitter)\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-x\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/x.com\\\"}],[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"YouTube\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-youtube\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/www.youtube.com\\\"}],[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"Instagram\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-linkedin\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/www.linkedin.com\\\"}]]', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-social_links'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"name\",\"value\":\"Facebook\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-facebook\"},{\"key\":\"url\",\"value\":\"https:\\/\\/www.facebook.com\"}],[{\"key\":\"name\",\"value\":\"<PERSON> (Twitter)\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-x\"},{\"key\":\"url\",\"value\":\"https:\\/\\/x.com\"}],[{\"key\":\"name\",\"value\":\"YouTube\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-youtube\"},{\"key\":\"url\",\"value\":\"https:\\/\\/www.youtube.com\"}],[{\"key\":\"name\",\"value\":\"Instagram\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-linkedin\"},{\"key\":\"url\",\"value\":\"https:\\/\\/www.linkedin.com\"}]]", "2025-08-14 23:46:44", "theme-martfury-social_links"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.321858, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 47.426, "width_percent": 0.322}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"facebook\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-facebook\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"x\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-x\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"pinterest\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-pinterest\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"linkedin\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-linkedin\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"whatsapp\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-whatsapp\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"email\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-mail\\\"}]]', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-social_sharing'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"social\",\"value\":\"facebook\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-facebook\"}],[{\"key\":\"social\",\"value\":\"x\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-x\"}],[{\"key\":\"social\",\"value\":\"pinterest\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-pinterest\"}],[{\"key\":\"social\",\"value\":\"linkedin\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-linkedin\"}],[{\"key\":\"social\",\"value\":\"whatsapp\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-whatsapp\"}],[{\"key\":\"social\",\"value\":\"email\"},{\"key\":\"icon\",\"value\":\"ti ti-mail\"}]]", "2025-08-14 23:46:44", "theme-martfury-social_sharing"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.327223, "duration": 0.010150000000000001, "duration_str": "10.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 47.748, "width_percent": 0.78}, {"sql": "update `settings` set `value` = '#fcb800', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-primary_color'", "type": "query", "params": [], "bindings": ["#fcb800", "2025-08-14 23:46:44", "theme-martfury-primary_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.338544, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 48.527, "width_percent": 0.337}, {"sql": "update `settings` set `value` = 'general/logo-light.png', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-admin_logo'", "type": "query", "params": [], "bindings": ["general/logo-light.png", "2025-08-14 23:46:44", "theme-martfury-admin_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.344065, "duration": 0.01602, "duration_str": "16.02ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 48.864, "width_percent": 1.231}, {"sql": "update `settings` set `value` = 'general/favicon.png', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'theme-martfury-admin_favicon'", "type": "query", "params": [], "bindings": ["general/favicon.png", "2025-08-14 23:46:44", "theme-martfury-admin_favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.362149, "duration": 0.01569, "duration_str": "15.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 50.095, "width_percent": 1.205}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'is_completed_get_started'", "type": "query", "params": [], "bindings": ["1", "2025-08-14 23:46:44", "is_completed_get_started"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.3805408, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 51.3, "width_percent": 0.356}, {"sql": "update `settings` set `value` = 'Pay online via Razorpay', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'payment_razorpay_name'", "type": "query", "params": [], "bindings": ["Pay online via Razorpay", "2025-08-14 23:46:44", "payment_razorpay_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.3871002, "duration": 0.019579999999999997, "duration_str": "19.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 51.656, "width_percent": 1.504}, {"sql": "update `settings` set `value` = 'Payment with <PERSON><PERSON><PERSON>y', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'payment_razorpay_description'", "type": "query", "params": [], "bindings": ["Payment with Razorpay", "2025-08-14 23:46:44", "payment_razorpay_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.409024, "duration": 0.01266, "duration_str": "12.66ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 53.16, "width_percent": 0.972}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'payment_razorpay_logo'", "type": "query", "params": [], "bindings": ["", "2025-08-14 23:46:44", "payment_razorpay_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.423035, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 54.133, "width_percent": 0.329}, {"sql": "update `settings` set `value` = 'rzp_test_WdWDmIqADKKMMT', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'payment_razorpay_key'", "type": "query", "params": [], "bindings": ["rzp_test_WdWDmIqADKKMMT", "2025-08-14 23:46:44", "payment_razorpay_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.429026, "duration": 0.00478, "duration_str": "4.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 54.462, "width_percent": 0.367}, {"sql": "update `settings` set `value` = 'bYnsMUuTcxNwicoDXwJmAj1e', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'payment_razorpay_secret'", "type": "query", "params": [], "bindings": ["bYnsMUuTcxNwicoDXwJmAj1e", "2025-08-14 23:46:44", "payment_razorpay_secret"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.435164, "duration": 0.01093, "duration_str": "10.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 54.829, "width_percent": 0.84}, {"sql": "update `settings` set `value` = '[]', `settings`.`updated_at` = '2025-08-14 23:46:44' where `key` = 'payment_razorpay_available_countries'", "type": "query", "params": [], "bindings": ["[]", "2025-08-14 23:46:44", "payment_razorpay_available_countries"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 380}, {"index": 15, "namespace": null, "name": "vendor/botble/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php", "line": 343}, {"index": 16, "namespace": null, "name": "vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1755215204.448353, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 55.668, "width_percent": 0.319}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.4538822, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 55.987, "width_percent": 0.304}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.458169, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 56.291, "width_percent": 0.326}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.46302, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 56.618, "width_percent": 0.334}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.4676979, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 56.952, "width_percent": 0.315}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.472118, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 57.267, "width_percent": 0.326}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.476752, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 57.593, "width_percent": 0.31}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.481153, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 57.904, "width_percent": 0.318}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.48567, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 58.222, "width_percent": 0.308}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.4901278, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 58.53, "width_percent": 0.316}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.494593, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 58.845, "width_percent": 0.34}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.4993958, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 59.185, "width_percent": 0.341}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.50417, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 59.527, "width_percent": 0.31}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.50851, "duration": 0.01821, "duration_str": "18.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 59.837, "width_percent": 1.399}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.5270948, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 61.236, "width_percent": 0.357}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.5322769, "duration": 0.02004, "duration_str": "20.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 61.593, "width_percent": 1.539}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.552667, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 63.132, "width_percent": 0.327}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.5572631, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 63.459, "width_percent": 0.295}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.561464, "duration": 0.00467, "duration_str": "4.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 63.754, "width_percent": 0.359}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.566691, "duration": 0.01233, "duration_str": "12.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 64.113, "width_percent": 0.947}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.579432, "duration": 0.004719999999999999, "duration_str": "4.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 65.06, "width_percent": 0.363}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.5845292, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 65.423, "width_percent": 0.317}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.589032, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 65.74, "width_percent": 0.326}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.593569, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 66.065, "width_percent": 0.313}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.598014, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 66.378, "width_percent": 0.317}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.602513, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 66.695, "width_percent": 0.319}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.607027, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 67.014, "width_percent": 0.296}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.611282, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 67.31, "width_percent": 0.319}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.615784, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 67.629, "width_percent": 0.321}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.620352, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 67.95, "width_percent": 0.317}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.624831, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 68.267, "width_percent": 0.343}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.629678, "duration": 0.00475, "duration_str": "4.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 68.609, "width_percent": 0.365}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.6348028, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 68.974, "width_percent": 0.314}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.6392748, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 69.288, "width_percent": 0.343}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.6440752, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 69.632, "width_percent": 0.32}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.6486259, "duration": 0.01235, "duration_str": "12.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 69.952, "width_percent": 0.949}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.661403, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 70.901, "width_percent": 0.337}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.666171, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 71.238, "width_percent": 0.313}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.6706321, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 71.551, "width_percent": 0.333}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.6753938, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 71.884, "width_percent": 0.327}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.680106, "duration": 0.019010000000000003, "duration_str": "19.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 72.211, "width_percent": 1.46}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.6994789, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 73.671, "width_percent": 0.311}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.70393, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 73.982, "width_percent": 0.328}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.708486, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 74.31, "width_percent": 0.295}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.7127001, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 74.605, "width_percent": 0.361}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.7177691, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 74.966, "width_percent": 0.308}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.722112, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 75.274, "width_percent": 0.31}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.7264829, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 75.585, "width_percent": 0.303}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.730845, "duration": 0.01464, "duration_str": "14.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 75.887, "width_percent": 1.125}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.74602, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 77.012, "width_percent": 0.349}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.7509341, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 77.361, "width_percent": 0.319}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.7554448, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 77.679, "width_percent": 0.303}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.7597249, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 77.983, "width_percent": 0.301}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.76412, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 78.284, "width_percent": 0.35}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.769041, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 78.634, "width_percent": 0.309}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.7733932, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 78.943, "width_percent": 0.306}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.777739, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 79.249, "width_percent": 0.334}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.782525, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 79.583, "width_percent": 0.312}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.7869809, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 79.895, "width_percent": 0.312}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.791386, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 80.206, "width_percent": 0.3}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.795635, "duration": 0.00484, "duration_str": "4.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 80.507, "width_percent": 0.372}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.8008168, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 80.879, "width_percent": 0.29}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.804909, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 81.168, "width_percent": 0.287}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.80903, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 81.455, "width_percent": 0.308}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.813645, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 81.763, "width_percent": 0.314}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.8180811, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 82.078, "width_percent": 0.317}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.82254, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 82.395, "width_percent": 0.308}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.8268569, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 82.703, "width_percent": 0.287}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.83097, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 82.989, "width_percent": 0.313}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.83538, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 83.302, "width_percent": 0.285}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.839419, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 83.587, "width_percent": 0.303}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.843729, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 83.89, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.847982, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 84.188, "width_percent": 0.313}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.8524148, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 84.501, "width_percent": 0.301}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.856636, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 84.802, "width_percent": 0.296}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.8607929, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 85.098, "width_percent": 0.299}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.8650331, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 85.397, "width_percent": 0.322}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.869547, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 85.718, "width_percent": 0.3}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.873751, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 86.019, "width_percent": 0.29}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.8779242, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 86.308, "width_percent": 0.331}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.88285, "duration": 0.00523, "duration_str": "5.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 86.639, "width_percent": 0.402}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.888393, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 87.041, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.892633, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 87.338, "width_percent": 0.295}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.896975, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 87.633, "width_percent": 0.336}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.901812, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 87.97, "width_percent": 0.347}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.9066641, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 88.317, "width_percent": 0.306}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.910955, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 88.623, "width_percent": 0.296}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.915163, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 88.918, "width_percent": 0.3}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.9194062, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 89.219, "width_percent": 0.306}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.9237032, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 89.525, "width_percent": 0.291}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.927824, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 89.816, "width_percent": 0.293}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.932004, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 90.11, "width_percent": 0.303}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.9362938, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 90.412, "width_percent": 0.343}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.941096, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 90.755, "width_percent": 0.285}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.945122, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 91.04, "width_percent": 0.417}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.950924, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 91.457, "width_percent": 0.316}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.955371, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 91.773, "width_percent": 0.285}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.9595811, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 92.058, "width_percent": 0.318}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.964383, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 92.376, "width_percent": 0.33}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.96904, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 92.706, "width_percent": 0.322}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.973557, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 93.028, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.977741, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 93.325, "width_percent": 0.295}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.9819639, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 93.62, "width_percent": 0.316}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.986481, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 93.936, "width_percent": 0.347}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.991352, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 94.283, "width_percent": 0.298}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755215204.9958022, "duration": 0.00784, "duration_str": "7.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 94.581, "width_percent": 0.602}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.004254, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 95.183, "width_percent": 0.323}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.008879, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 95.507, "width_percent": 0.296}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.013309, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 95.802, "width_percent": 0.33}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0180168, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 96.132, "width_percent": 0.303}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.022325, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 96.434, "width_percent": 0.3}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.026622, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 96.735, "width_percent": 0.306}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0310411, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 97.041, "width_percent": 0.306}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0354838, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 97.348, "width_percent": 0.329}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.040121, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 97.676, "width_percent": 0.305}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.04441, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 97.981, "width_percent": 0.307}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.048851, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 98.289, "width_percent": 0.333}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.05363, "duration": 0.01396, "duration_str": "13.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 98.622, "width_percent": 1.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0680032, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 99.694, "width_percent": 0.306}]}, "models": {"data": {"Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/admin/plugins/status?name=erpnext", "action_name": "plugins.change.status", "controller_action": "Botble\\PluginManagement\\Http\\Controllers\\PluginManagementController@update", "uri": "PUT admin/plugins/status", "permission": "plugins.index", "controller": "Botble\\PluginManagement\\Http\\Controllers\\PluginManagementController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplugin-management%2Fsrc%2FHttp%2FControllers%2FPluginManagementController.php&line=93\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\PluginManagement\\Http\\Controllers", "prefix": "admin/plugins", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplugin-management%2Fsrc%2FHttp%2FControllers%2FPluginManagementController.php&line=93\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php:93-130</a>", "middleware": "web, core, auth, preventDemo", "duration": "2.67s", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1931679634 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">erpnext</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931679634\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1564112053 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">put</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1564112053\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1809329057 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjBNUisvMFFrN1owYjVuNkkwL1VLTmc9PSIsInZhbHVlIjoieUFrelRiVDJHMkpEMTBHSDhCY1FKbDRnWUZBbTE2aXJiOHY2eXNyRlFyaDh4WnZ5K05zTzF4VldMdnFES3U4dzRBRjc2Tll3SzdXTWFvWmNwOXhBcWc2c2pESkIyWDAweVNqcVZUWTdlMnZ2YWt1ZG0yK2p5NG1rTVowU2lUbkEiLCJtYWMiOiI5NGE0NTJhOWE5ODM2ZjNmYmNiYjA1YjNjNmVmZTc4MGE0OWYzZDdhYjFiMTdhYmZjNjhhMTkyNzE0NWRlZDIxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">https://martfury.gc/admin/plugins/installed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3267 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; xxx111otrckid=b667792b-b878-4cd5-a326-3ac82d49d6c8; XSRF-TOKEN=eyJpdiI6IjBNUisvMFFrN1owYjVuNkkwL1VLTmc9PSIsInZhbHVlIjoieUFrelRiVDJHMkpEMTBHSDhCY1FKbDRnWUZBbTE2aXJiOHY2eXNyRlFyaDh4WnZ5K05zTzF4VldMdnFES3U4dzRBRjc2Tll3SzdXTWFvWmNwOXhBcWc2c2pESkIyWDAweVNqcVZUWTdlMnZ2YWt1ZG0yK2p5NG1rTVowU2lUbkEiLCJtYWMiOiI5NGE0NTJhOWE5ODM2ZjNmYmNiYjA1YjNjNmVmZTc4MGE0OWYzZDdhYjFiMTdhYmZjNjhhMTkyNzE0NWRlZDIxIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IkFMek5hWWQva2ltdnl5RVFnTktPUXc9PSIsInZhbHVlIjoiUjUyRVJPR0cyN2FuVVZrMEoydnI5L20zN1Y0R1FNeDJ6QjUyUGtqVnQyV3hSV28yTWhaMUtzeUxMT05NWEx5YW9nZkZ1RUxBZTdyT1Bqdkw4cUpteVZ2TWN1ZVQvMFI3aFl6SEVRUkpxRWh5SWZJaTljZnZqYmcydHlZK3pEWHgiLCJtYWMiOiJhZWU4YWJlYTU1NmVjYzM2MTU0NzdjMzgzZTU4YjY1YmI1ZTJiYTNjZTc4MTcwYmVhY2NmNDIyNDllYjc0MGRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1809329057\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-414861458 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>xxx111otrckid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dhXq3H0q2A1IwAt71xnTkmOnVhkoiBuxG1NoJ54S</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6o7K5Vt18ObXQZVJQrMB8hUNn5eNuW4gUpBf0vtK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-414861458\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1916947974 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 23:46:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916947974\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-621297246 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dhXq3H0q2A1IwAt71xnTkmOnVhkoiBuxG1NoJ54S</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">https://martfury.gc/admin/plugins/installed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>7</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>-</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755214857\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755214857</span></span> {<a class=sf-dump-ref href=#sf-dump-621297246-ref24152 title=\"3 occurrences\">#4152</a><samp data-depth=3 id=sf-dump-621297246-ref24152 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010380000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 14, 2025\n- 00:05:47.711411 from now\nDST Off\">2025-08-14 23:40:57.400834 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4153</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>457dbff0be834a20b3903dcc369c5f6b</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4154</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">457dbff0be834a20b3903dcc369c5f6b</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>24</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>4</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"26 characters\">Dual Camera 20MP (Digital)</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>80.25</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4155</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products/1.jpg</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"25 characters\">(Color: Black, Size: XXL)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>VAT</span>\" => <span class=sf-dump-num>10.0</span>\n              </samp>]\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-118-A0</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>848.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>10.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755214857\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755214857</span></span> {<a class=sf-dump-ref>#4156</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000103c0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 14, 2025\n- 00:05:47.712013 from now\nDST Off\">2025-08-14 23:40:57.400788 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755214857\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755214857</span></span> {<a class=sf-dump-ref>#4157</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000103d0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 14, 2025\n- 00:05:47.712091 from now\nDST Off\">2025-08-14 23:40:57.400781 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">50da09dec9a537b0bbea6825c33a42fc</span>\"\n  \"<span class=sf-dump-key>c229db37147d462b82e0445cc4120575</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:24</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755214857\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755214857</span></span> {<a class=sf-dump-ref href=#sf-dump-621297246-ref24152 title=\"3 occurrences\">#4152</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>46</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755214857\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755214857</span></span> {<a class=sf-dump-ref href=#sf-dump-621297246-ref24152 title=\"3 occurrences\">#4152</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping</span>\" => []\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>shipping_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">hyperpay</span>\"\n  \"<span class=sf-dump-key>hyperpay_checkout_id</span>\" => \"<span class=sf-dump-str title=\"46 characters\">52561CC9FFD667A3FAB13BE4C31550F4.uat01-vm-tx02</span>\"\n  \"<span class=sf-dump-key>hyperpay_order_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>46</span>\n  </samp>]\n  \"<span class=sf-dump-key>hyperpay_amount</span>\" => <span class=sf-dump-num>353.1</span>\n  \"<span class=sf-dump-key>hyperpay_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SAR</span>\"\n  \"<span class=sf-dump-key>hyperpay_payment_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">visa</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621297246\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/admin/plugins/status?name=erpnext", "action_name": "plugins.change.status", "controller_action": "Botble\\PluginManagement\\Http\\Controllers\\PluginManagementController@update"}, "badge": null}}